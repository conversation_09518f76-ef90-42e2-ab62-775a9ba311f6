package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 工作流DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@Accessors(chain = true)
@QueryDomain("com.dcai.aixg.domain.aiagent.flow.FlowNode")
public class FlowNodeDTO {

    /**
     * 工作流类型枚举
     */
    @Getter
    public enum Type {
        GENERATE("生成器"),
        SUBFLOW("子流程"),
        ;

        private final String description;

        Type(String description) {
            this.description = description;
        }
    }

}
