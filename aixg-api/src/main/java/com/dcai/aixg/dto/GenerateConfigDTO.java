package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 生成配置DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@QueryDomain("com.dcai.aixg.domain.generate.GenerateConfig")
public class GenerateConfigDTO {

    /**
     * 配置ID
     */
    @QueryField
    private Long id;

    /**
     * 配置代码
     */
    @QueryField
    private String code;

    /**
     * 配置标题
     */
    @QueryField
    private String title;

    /**
     * 配置描述
     */
    @QueryField
    private String description;

    /**
     * 演示内容
     */
    @QueryField
    private String demo;

    /**
     * 系统模板
     */
    @QueryField
    private String systemTemplate;

    /**
     * 用户模板
     */
    @QueryField
    private String userTemplate;

    /**
     * 任务类型
     */
    @QueryField("generateType")
    private GenerateDTO.Type type;

    /**
     * 任务类型描述
     */
    private String taskTypeDescription;

    /**
     * 是否启用
     */
    @QueryField
    private Boolean enabled;

    /**
     * 排序
     */
    @QueryField
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @QueryField
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @QueryField
    private LocalDateTime updateTime;

}
