package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 工作流DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@QueryDomain("com.dcai.aixg.domain.flow.Flow")
public class FlowDTO {

    /**
     * 工作流类型枚举
     */
    @Getter
    public enum Type {
        SEQUENTIAL("顺序执行");

        private final String description;

        Type(String description) {
            this.description = description;
        }
    }

    /**
     * 工作流类型枚举
     */
    @Getter
    public enum SrcType {
        TASK("客户任务"),
        SUBFLOW("子流程"),
        TWEET("推文")
        ;

        private final String description;

        SrcType(String description) {
            this.description = description;
        }
    }

    /**
     * 工作流状态枚举
     */
    @Getter
    public enum Status {
        WAIT("待处理"),
        PROCESSING("处理中"),
        SUCCESS("成功"),
        FAILED("失败");

        private final String description;

        Status(String description) {
            this.description = description;
        }
    }

    /**
     * 工作流ID
     */
    @QueryField
    private Long id;

    /**
     * 工作流配置
     */
    @QueryField
    private FlowConfigDTO config;

    /**
     * 请求内容
     */
    @QueryField
    private String request;

    /**
     * 状态
     */
    @QueryField
    private Status status;

    /**
     * 当前步骤
     */
    @QueryField
    private Integer currentStep;

    /**
     * 总步骤数
     */
    @QueryField
    private Integer totalSteps;

    /**
     * 最终响应内容
     */
    @QueryField
    private String response;

    /**
     * 错误信息
     */
    @QueryField
    private String errorMessage;

    /**
     * 执行时长(毫秒)
     */
    @QueryField
    private Long duration;

    /**
     * 开始时间
     */
    @QueryField
    private LocalDateTime startedTime;

    /**
     * 完成时间
     */
    @QueryField
    private LocalDateTime completedTime;

    @QueryField
    private GenerateDTO lastGenerate;

}
