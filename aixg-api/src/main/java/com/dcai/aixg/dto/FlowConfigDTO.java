package com.dcai.aixg.dto;

import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流配置DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@QueryDomain("com.dcai.aixg.domain.flowconfig.FlowConfig")
public class FlowConfigDTO {

    /**
     * 配置ID
     */
    @QueryField
    private Long id;

    /**
     * 工作流代码
     */
    @QueryField
    private String code;

    /**
     * 工作流标题
     */
    @QueryField
    private String title;

    /**
     * 工作流描述
     */
    @QueryField
    private String description;

    /**
     * 创建时间
     */
    @QueryField
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @QueryField
    private LocalDateTime updateTime;

    /**
     * 包含的生成配置列表
     */
    private List<GenerateConfigDTO> generateConfigs;

    /**
     * 步骤数量
     */
    private Integer stepCount;

}
