package com.dcai.aixg.pro;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 根据配置代码创建生成任务请求参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Schema(description = "根据配置代码创建生成任务请求参数")
public class LaunchGeneratePO {

    @Getter
    public enum RunMode {
        SYNC("同步模式"), ASYNC("异步模式"), STREAM("流式模式");

        private final String description;

        RunMode(String description) {
            this.description = description;
        }
    }

    @Schema(description = "生成器配置代码")
    private String configCode;

    @Schema(description = "请求参数")
    private Map<String, Object> request;

    @Schema(description = "运行方式")
    private RunMode runMode = RunMode.ASYNC;

}
