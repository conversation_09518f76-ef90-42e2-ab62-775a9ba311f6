package com.dcai.aixg.pro.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreateComparePO {
	
    @Schema(name = "cityName", description = "城市名")
    private String cityName;
	
    @Schema(name = "regionName", description = "区域名")
	private String regionName;
	
    @Schema(name = "estateName", description = "小区名")
    private String estateName;
	
    @Schema(name = "areaName", description = "板块名")
    private String areaName;

}
