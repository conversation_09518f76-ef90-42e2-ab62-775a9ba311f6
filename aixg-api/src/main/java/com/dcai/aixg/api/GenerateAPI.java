package com.dcai.aixg.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.pro.LaunchGeneratePO;
import com.ejuetc.commons.base.response.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 生成相关API接口
 *
 * <AUTHOR>
 */
@Tag(name = "AI生成接口")
@FeignClient(name = "aixg", path = "/aixg", contextId = "generateAPI")
public interface GenerateAPI {

    @Operation(summary = "API_发起生成任务")
    @PostMapping("/api/generate/launch")
    ApiResponse<GenerateDTO> launch(@RequestBody @Valid LaunchGeneratePO po);

    @Operation(summary = "API_发起测试任务")
    @PostMapping("/api/generate/launch4test")
    ApiResponse<Long> launch4test(
            @Parameter(description = "DeepSeek,通义千问,豆包") @RequestParam String modeName,
            @Parameter(description = "应答类型(JSON或类名)") @RequestParam(required = false) String resultType,
            @RequestBody String prompt
    );

    @Operation(summary = "API_接收准备结果")
    @PostMapping("/api/generate/receivePrepareResult")
    ApiResponse<?> receiveAsyncResponse(@RequestParam Long generateId, @RequestParam GenerateDTO.Status status, @RequestBody String result);

    @Operation(summary = "API_查询生成结果")
    @PostMapping("/api/generate/queryResponse")
    ResponseEntity<String> queryResponse(@RequestParam Long id);

}
