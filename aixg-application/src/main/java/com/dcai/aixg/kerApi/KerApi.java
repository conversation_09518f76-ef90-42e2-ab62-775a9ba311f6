package com.dcai.aixg.kerApi;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.dcai.aixg.domain.search.Search;
import com.dcai.aixg.dto.GoodsDTO;
import com.dcai.aixg.dto.PointConfigDTO;
import com.dcai.aixg.dto.PointDetailDTO;
import com.dcai.aixg.dto.search.ChatDetailDTO;
import com.dcai.aixg.dto.search.ChatListDTO;
import com.dcai.aixg.dto.search.HotSearchDTO;
import com.dcai.aixg.dto.task.ReportCreateDTO;
import com.dcai.aixg.dto.task.ReportDetailDTO;
import com.dcai.aixg.dto.task.TaskDTO;
import com.dcai.aixg.dto.task.WriteCreateDTO;
import com.dcai.aixg.dto.task.WriteDetailDTO;
import com.dcai.aixg.pro.search.ListQueryPO;
import com.dcai.aixg.pro.task.ReportCreatePO;
import com.dcai.aixg.pro.task.WriteCreateComparePO;
import com.dcai.aixg.pro.task.WriteCreatePO;
import com.dcai.aixg.pro.task.WriteDataCreatePO;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.Page;
import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

@Component
@Slf4j
public class KerApi {

    @Value("${dcai.aixg.ker.url}")
    private String baseUrl;

    @Value("${dcai.aixg.ker.client_id}")
    private String clientId;

    @Value("${dcai.aixg.ker.client_secret}")
    private String clientSecret;

    public static final String KERTOKEN = "KER_TOKEN";

    private static final RestTemplate restTemplate = new RestTemplate();

    private String getToken() {
        Long expire = getBean(RedisTemplate.class).getExpire(KERTOKEN);
        if (expire != null && expire > 60) {
            return getBean(RedisTemplate.class).opsForValue().get(KERTOKEN).toString();
        }
        String resp = restTemplate.getForObject(baseUrl + KerApiUrl.TOKEN.getTitle(), String.class, clientId, clientSecret);
        JSONObject responseObj = JSONObject.parseObject(resp);
        if (!"0".equals(responseObj.getString("code"))) {
            throw new CodingException("获取克而瑞 TOKEN失败:" + responseObj.getString("message"));
        }

        Token tokenObj = responseObj.getObject("data", Token.class);
        String token = tokenObj.tokenType + " " + tokenObj.accessToken;
        getBean(RedisTemplate.class).opsForValue().set(KERTOKEN, token, tokenObj.expiresIn, TimeUnit.SECONDS);
        return token;
    }

    public Long getKerUserId(String phone) {
        JSONObject responseObj = send(String.format(KerApiUrl.USER_CREATE.getTitle(), phone), HttpMethod.POST, null);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        if (!dataJson.containsKey("id")) {
            throw new CodingException("克而瑞用户数据异常!");
        }
        return dataJson.getLong("id");
    }

    public Long getUserPoint(Long userId) {
        JSONObject responseObj = send(String.format(KerApiUrl.USER_POINT.getTitle(), userId), HttpMethod.GET, null);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        if (!dataJson.containsKey("points")) {
            log.error("查询用户{}积分失败", userId);
            return 0L;
        }
        return dataJson.getLong("points");
    }

    public List<PointConfigDTO> getPointConfig(String category) {
        String url = StringUtils.isBlank(category) ? KerApiUrl.POINT_CONFIG.getTitle() : KerApiUrl.POINT_CONFIG.getTitle()  +   "?category=" + category;
        JSONObject responseObj = send(url, HttpMethod.GET, null);
        JSONArray jsonArray = responseObj.getJSONArray("data");
        if (StringUtils.isBlank(category)) {
            List<PointConfigDTO.PointConfigDetail> details = jsonArray.stream()
                    .map(obj -> (JSONObject) obj)
                    .map(jsonObject -> {
                        return new PointConfigDTO.PointConfigDetail().setCode(jsonObject.getString("code"))
                                .setTitle(jsonObject.getString("name"))
                                .setUse(jsonObject.getString("value"))
                                .setDescription(jsonObject.getString("describe"));
                    })
                    .collect(Collectors.toList());
            return List.of(new PointConfigDTO().setName("积分消耗配置").setDescribe(details));
        } else {
            return jsonArray.stream()
                    .map(obj -> (JSONObject) obj)
                    .map(jsonObject -> {
                        List<PointConfigDTO.PointConfigDetail> details = jsonObject.getJSONArray("describe")
                                .stream()
                                .map(obj1 -> (JSONObject) obj1)
                                .map(jsonObject1 -> new PointConfigDTO.PointConfigDetail()
                                        .setTitle(jsonObject1.getString("title"))
                                        .setUse(jsonObject1.getString("use"))
                                        .setDescription(jsonObject1.getString("description"))
                                        .setSummary(jsonObject1.getString("summary"))
                                ).collect(Collectors.toList());
                        return new PointConfigDTO().setName(jsonObject.getString("name"))
                                .setDescribe(details);
                    })
                    .collect(Collectors.toList());
        }
    }

    public ApiResponse<List<PointDetailDTO>> getPointDetail(Long userId, Integer page, Integer pageSize) {
        JSONObject responseObj = send(String.format(KerApiUrl.POINT_DETAIL.getTitle(), userId, page, pageSize), HttpMethod.GET, null);
        String data = responseObj.getString("data");
        JSONObject jsonObject = JSONObject.parseObject(data);
        Page responsePage = new Page(page, pageSize).setTotalRow(jsonObject.getLong("total"));
        JSONArray jsonArray = jsonObject.getJSONArray("list");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        List<PointDetailDTO> pointDetailDTOS = jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .map(jsonObject1 -> new PointDetailDTO()
                        .setId(jsonObject1.getLong("id"))
                        .setCreateTime(LocalDateTime.parse(jsonObject1.getString("createTime"), formatter))
                        .setType(jsonObject1.getJSONObject("detail").getString("type"))
                        .setTitle(jsonObject1.getJSONObject("detail").getString("title"))
                        .setDesc(jsonObject1.getJSONObject("detail").getString("desc"))
                        .setPoint(jsonObject1.getLong("point"))
                ).collect(Collectors.toList());
        return ApiResponse.succ(pointDetailDTOS).setPage(responsePage);
    }
    
    public List<HotSearchDTO> getHotSearch() {
        JSONObject responseObj = send(KerApiUrl.HOT_SEARCH.getTitle(), HttpMethod.GET, null);
        String data = responseObj.getString("data");
        JSONArray jsonArray = JSONArray.parseArray(data);
        List<HotSearchDTO> result = jsonArray.stream().map(item -> {
        	JSONObject json = (JSONObject) item;
        	return new HotSearchDTO(json.getString("title"));
        }).collect(Collectors.toList());
        return result;
    }
    
    public SseEmitter doSearch(Long userId, String chatId, String query, Search search) {
    	SseEmitter result = null;
		try {
			if (StringUtils.isEmpty(chatId)) chatId = chatCreate(userId, query);
			search.setChatId(chatId);
			result = chatStart(userId, chatId, query);
		} catch (BusinessException e) {
			if (result == null) result = new SseEmitter(Long.MAX_VALUE);
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("type", "error");
			jsonObj.put("message", e.getMessage());
			try {
				result.send(jsonObj.toJSONString());
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			result.complete();
			//result.completeWithError(e);
		}
    	return result;
    }
    
    public String chatCreate(Long userId, String title) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("title", title);
        JSONObject responseObj = send(KerApiUrl.CHAT_CREATE.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson.getString("chatId");
    }
    
    public SseEmitter chatStart(Long userId, String chatId, String query) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("chatId", chatId);
        body.put("query", query);
        SseEmitter sseEmitter = buildSseEmitter(KerApiUrl.CHAT_START.getTitle(), body);
    	return sseEmitter;
    }
    
    public ApiResponse<List<ChatListDTO>> chatList(Long userId, String keyword, Integer page, Integer pageSize) {
    	Map<String, Object> body = new HashMap<>();
        body.put("keyword", keyword);
        body.put("page", page);
        body.put("pageSize", pageSize);
        JSONObject responseObj = send(String.format(KerApiUrl.CHAT_LIST.getTitle(), userId), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        List<JSONObject> list = dataJson.getJSONArray("list").toJavaList(JSONObject.class);
        List<ChatListDTO> result = list.stream().map(item -> {
        	String message = item.getString("message");
        	if (StringUtils.isNotEmpty(message)) message = message.trim().replaceAll("^\"|\"$", "");
        	return new ChatListDTO().setChatId(item.getString("id"))
        			.setTitle(item.getString("title"))
        			.setMessage(message)
        			.setCreated_at(item.getString("createdAt"))
        			.setCreated_at_show(buildCreated_at_show(item.getString("createdAt")));
        }).sorted(Comparator.comparing(ChatListDTO::getCreated_at, Comparator.reverseOrder())).collect(Collectors.toList());        
        Page responsePage = result.size() == 0 ? 
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")) :
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")).setTotalRow(dataJson.getLong("total"));
        return ApiResponse.succ(result).setPage(responsePage);
    }
    
    public String buildCreated_at_show(String created_at) {
    	if (created_at == null) return "";
    	created_at = created_at.replace("T", " ");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTimeStr = LocalDateTime.parse(created_at, formatter);
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(dateTimeStr, now);
        long days = duration.toDays();
        long hours = duration.toHours();
        long minutes = duration.toMinutes();
        long seconds = duration.toSeconds();
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        if (days >= 7) return dateTimeStr.format(formatter1);
        if (days >= 1) return days + "天前";
        if (hours >= 1) return hours + "小时前";
        if (minutes >= 1) return minutes + "分钟前";
        if (seconds >= 1) return seconds + "秒前";
        return "";
    }
    
    public ChatDetailDTO chatDetail(Long userId, String chatId, Integer limit, String lastMsgId) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("chatId", chatId);
        body.put("limit", limit);
        body.put("lastMsgId", lastMsgId);
        JSONObject responseObj = send(KerApiUrl.CHAT_DETAIL.getTitle(), HttpMethod.POST, body);
        JSONObject dataJson = (JSONObject) responseObj.get("data");
        ChatDetailDTO result = new ChatDetailDTO();
        result.setLastMsgId(dataJson.getString("lastMsgId"));
        result.setHasMore(dataJson.getString("hasMore"));
        List<JSONObject> list = new ArrayList<>();
        if (dataJson.getJSONArray("list").size() > 0) {
            for (int i = 0; i < dataJson.getJSONArray("list").size(); i++) {
                list.add(dataJson.getJSONArray("list").getJSONObject(i));
            }
        }
        result.setList(list);
        return result;
    }
    
    public ApiResponse<List<WriteDetailDTO>> writeList(Long userId, ListQueryPO po) {
    	DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        Map<String, Object> body = new HashMap<>();
        body.put("status", buildKerStatus(po.getStatus()));
        body.put("typeSub", po.getWriteType());
        if (po.getStartDate() != null) body.put("startDate", po.getStartDate().format(formatter));
        if (po.getEndDate() != null) body.put("endDate", po.getEndDate().format(formatter));
        body.put("keyword", po.getKeyword());
        body.put("page", po.getPage());
        body.put("pageSize", po.getPageSize());
        JSONObject responseObj = send(String.format(KerApiUrl.WRITE_LIST.getTitle(), userId), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        List<WriteDetailDTO> result = dataJson.getJSONArray("list").toJavaList(JSONObject.class)
        		.stream().map(item -> new WriteDetailDTO()
        				.setArticleId(item.getString("typeSub").equals("6") ? null : item.getString("articleId"))
        				.setWriteId(item.getString("typeSub").equals("6") ? null : item.getString("writeId"))
        				.setReportId(item.getString("typeSub").equals("6") ? item.getString("writeId") : null)
        				.setTaskStatus(buildTaskStatus(item.getString("status")))
        				.setTaskStatusName(buildTaskStatusName(item.getString("status")))
        				.setWriteType(item.getString("typeSub"))
        				.setWriteTypeName(item.getString("typeSub").equals("6") ? "二手房估价专家" : item.getString("typeName"))
//        				.setReportType(item.getString("typeSub"))
//        				.setReportTypeName(item.getString("typeName"))
        				.setTitle(item.getString("title"))
        				.setAsk(item.getString("ask"))
        				.setSummary(item.getString("summary"))
        				.setCreatedAt(item.getString("createdAt"))
        				.setCreatedAtShow(buildCreated_at_show(item.getString("createdAt")))
        				.setContent(item.getString("content")))
        		.sorted(Comparator.comparing(WriteDetailDTO::getCreatedAt, Comparator.reverseOrder())).collect(Collectors.toList());
        Page responsePage = result.size() == 0 ? 
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")) :
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")).setTotalRow(dataJson.getLong("total"));
        return ApiResponse.succ(result).setPage(responsePage);
    }
    
    public String buildTaskStatus(String status) {
        return switch (status) {
            case "1" -> "ING";
            case "2" -> "DONE";
            case "3" -> "FAIL";
            default -> status;
        };
    }
    
    public String buildTaskStatusName(String status) {
        return switch (status) {
            case "1" -> "生成中";
            case "2" -> "已生成";
            case "3" -> "生成失败";
            default -> status;
        };
    }
    
    public String buildTpeSubName(String typeSub) {
        return switch (typeSub) {
            case "1" -> "小区专家";
            case "2" -> "区域板块专家";
            case "3" -> "房源百科专家";
            case "4" -> "市场政策解读专家";
            case "5" -> "城市置业解读专家";
            case "6" -> "二手房价格评测报告";
            default -> typeSub;
        };
    }
    
    public String buildKerStatus(TaskDTO.Status status) {
    	if (status == null) return "0";
        return switch (status) {
            case ING -> "1";
            case DONE -> "2";
            case FAIL -> "3";
            default -> "0";
        };
    }
    
    public WriteDetailDTO writeDetail(Long userId, String writeId) {
        JSONObject responseObj = send(String.format(KerApiUrl.WRITE_DETAIL.getTitle(), userId, writeId), HttpMethod.GET, null);
        if (!responseObj.getString("code").equals("0")) throw new BusinessException("bc.cpm.aixg.1010", responseObj.getString("message"));
        String data = responseObj.getString("data");
        JSONObject item = JSONObject.parseObject(data);
        WriteDetailDTO result = new WriteDetailDTO()
			//.setArticleId(item.getString("id"))
			.setWriteId(item.getString("id"))
			.setTaskStatus(buildTaskStatus(item.getString("status")))
			.setTaskStatusName(buildTaskStatusName(item.getString("status")))
			.setWriteType(item.getString("typeSub"))
			.setWriteTypeName(item.getString("typeSub"))
			.setTitle(item.getString("title"))
			.setAsk(item.getString("ask"))
			.setSummary(item.getString("summary"))
			.setCreatedAt(item.getString("createdAt"))
			.setCreatedAtShow(buildCreated_at_show(item.getString("createdAt")))
			.setContent(item.getString("content"));
        return result;
    }
    
    public boolean chatDelete(Long userId, String chatId) {
    	Map<String, Object> body = new HashMap<>();
        JSONObject responseObj = send(String.format(KerApiUrl.CHAT_DELETE.getTitle(), userId, chatId), HttpMethod.POST, body);
        if (responseObj.getString("code").equals("0")) return true;
        return false;
    }
    
    public boolean reportDelete(Long userId, String reportId) {
    	Map<String, Object> body = new HashMap<>();
        JSONObject responseObj = send(String.format(KerApiUrl.REPORT_DELETE.getTitle(), userId, reportId), HttpMethod.POST, body);
        if (responseObj.getString("code").equals("0")) return true;
        return false;
    }
    
    public boolean writeDelete(Long userId, String writeId) {
    	Map<String, Object> body = new HashMap<>();
        JSONObject responseObj = send(String.format(KerApiUrl.WRITE_DELETE.getTitle(), userId, writeId), HttpMethod.POST, body);
        if (responseObj.getString("code").equals("0")) return true;
        return false;
    }
    
    public WriteCreateDTO doWrite(Long userId, WriteCreatePO po) {
    	String writeId = writeCreate(userId, po.getWriteType());
    	String articleId = writeSubmit(userId, writeId, po);
        return new WriteCreateDTO().setWriteId(writeId).setArticleId(articleId);
    }
    
    public String writeSubmit(Long userId, String writeId, WriteCreatePO po) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("writeId", writeId);
        body.put("houseInfo", po.getHouseInfo());
        body.put("cityName", po.getCityName());
        body.put("areaName", po.getAreaName());
        body.put("addInfo", po.getAddInfo());
        body.put("style", StringUtils.isNotBlank(po.getStyle()) ? po.getStyle() : "1");
        body.put("policy", po.getPolicy());
        body.put("lifeInfo", po.getLifeInfo());
        body.put("compareType", po.getCompareType());
        body.put("compareA", buildCompareJson(po.getCompareType(), po.getHouseCompareA(), po.getCompareA()));
        body.put("compareB", buildCompareJson(po.getCompareType(), po.getHouseCompareB(), po.getCompareB()));
//        body.put("regionName", po.getRegionName());
//        body.put("estateName", po.getEstateName());
//        body.put("policyTime", po.getPolicyTime());
//        body.put("facilities", po.getFacilities());
        JSONObject responseObj = send(KerApiUrl.WRITE_SUBMIT.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson.getString("articleId");
    }
    
    private JSONObject buildCompareJson(String compareType, JSONObject houseCompare, WriteCreateComparePO otherCompare) {
    	if (StringUtils.isBlank(compareType) || !(compareType.equals("1") || compareType.equals("2") || compareType.equals("3"))) return null;
    	if (compareType.equals("1") && houseCompare == null) return null;
    	if ((compareType.equals("2") || compareType.equals("3")) && otherCompare == null) return null;
    	JSONObject result = new JSONObject();
    	if (compareType.equals("1")) {
    		//房源对比维度：总价、单价测算、房屋年代、物业类型、房源面积、户型、户型图、房源实拍、朝向、楼层、梯户、车位、装修、房本年限
    		result.put("总价", houseCompare.getString("priceTotal") + "万");
    		result.put("单价测算", houseCompare.getString("priceUnit") + "元/平方米");
    		result.put("房屋年代", houseCompare.getString("completionTime") + "年");
    		result.put("物业类型", houseCompare.getString("propertyYears") + "年产权" + houseCompare.getString("propertyType"));
    		result.put("房源面积", houseCompare.getString("buildingArea"));
    		result.put("户型", houseCompare.getString("layoutName"));
    		String layoutImage = null;
    		String realImage = null;
    		JSONObject mediasMap = houseCompare.getJSONObject("mediasMap");
    		if (mediasMap != null) {
    			JSONArray layoutImages = mediasMap.getJSONArray("户型图");
    			if (layoutImages != null && layoutImages.size() > 0) {
    				layoutImage = layoutImages.getString(0);
    			}
    			JSONArray realImages = mediasMap.getJSONArray("实景图");
    			if (realImages != null && realImages.size() > 0) {
    				realImage = realImages.getString(0);
    			}
    		}
    		result.put("户型图", layoutImage);
    		result.put("房源实拍", realImage);
    		result.put("朝向", houseCompare.getString("orient"));
    		result.put("楼层", houseCompare.getString("currentFloor"));
    		result.put("梯户", houseCompare.getString("priceUnit"));
    		result.put("车位", houseCompare.getString("parkingRatio"));
    		result.put("装修", houseCompare.getString("redo"));
    		result.put("房本年限", houseCompare.getString("propertyYears"));
    	} else if (compareType.equals("2")) {
    		//小区对比维度：容积率、绿化率、房屋总数、车位总数、建成年代（取值建成年代最小）、车位配比（取值车位配比率）、物业费、停车费（取值固定停车费标准）、是否人车分流、建筑类别、房屋类型、占地面积、建筑面积
    		//小区对比入参：城市、区域、小区
    		result.put("城市", otherCompare.getCityName());
    		result.put("区域", otherCompare.getRegionName());
    		result.put("小区", otherCompare.getEstateName());
    	} else if (compareType.equals("3")) {
    		//区域板块对比维度：所属区域板块、地铁、拥堵情况、交通利好、大型商场、超市、学校、医院
    		//小区对比入参：城市、区域、版块
    		result.put("城市", otherCompare.getCityName());
    		result.put("区域", otherCompare.getRegionName());
    		result.put("版块", otherCompare.getAreaName());
    	} else {
    		return null;
    	}
    	return result;
    }
    
    public String writeCreate(Long userId, String writeType) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("writeType", writeType);
        JSONObject responseObj = send(KerApiUrl.WRITE_CREATE.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson.getString("writeId");
    }
    
    public String writeChat(Long userId, String writeId, String ask) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("writeId", writeId);
        body.put("ask", ask);
        JSONObject responseObj = send(KerApiUrl.WRITE_CHAT.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson.getString("title");
    }
    
    public ApiResponse<List<JSONObject>> reportList(Long userId, String keyword, Integer page, Integer pageSize) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("keyword", keyword);
        body.put("page", page);
        body.put("pageSize", pageSize);
        JSONObject responseObj = send(KerApiUrl.REPORT_LIST.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        List<JSONObject> result = dataJson.getJSONArray("list").toJavaList(JSONObject.class);
        Page responsePage = result.size() == 0 ? 
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")) :
        		new Page(dataJson.getInteger("page"), dataJson.getInteger("pageSize")).setTotalRow(dataJson.getLong("total"));
        return ApiResponse.succ(result).setPage(responsePage);
    }
    
    public ReportDetailDTO reportDetail(Long userId, String reportId) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("reportId", reportId);
        JSONObject responseObj = send(KerApiUrl.REPORT_DETAIL.getTitle(), HttpMethod.POST, body);
        if (!responseObj.getString("code").equals("0")) throw new BusinessException("bc.cpm.aixg.1011", responseObj.getString("message"));
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        ReportDetailDTO result = new ReportDetailDTO()
        		.setReportId(dataJson.getString("reportId"))
        		.setContent(dataJson.getString("content"));
        return result;
    }
    
    public JSONObject getData4PriceAssessment(WriteDataCreatePO po) {
        Map<String, Object> body = new HashMap<>();
        body.put("cityName", po.getCityName());
        body.put("projectName", po.getProjectName());
        body.put("projectId", po.getProjectId());
        body.put("regionName", po.getRegionName());
        body.put("areaName", po.getAreaName());
        body.put("layout", po.getLayout());
        JSONObject responseObj = send(KerApiUrl.DATA_PRICE_ASSESSMENT.getTitle(), HttpMethod.POST, body);
        if (!responseObj.getString("code").equals("0")) throw new BusinessException("bc.cpm.aixg.1011", responseObj.getString("message"));
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson;
    }
    
    public JSONObject getData4CommunityCompare(WriteDataCreatePO po) {
        Map<String, Object> body = new HashMap<>();
        body.put("cityName", po.getCityName());
        body.put("projectName", po.getProjectName());
        body.put("projectId", po.getProjectId());
        body.put("regionName", po.getRegionName());
        body.put("areaName", po.getAreaName());
        JSONObject responseObj = send(KerApiUrl.DATA_PRICE_ASSESSMENT.getTitle(), HttpMethod.POST, body);
        if (!responseObj.getString("code").equals("0")) throw new BusinessException("bc.cpm.aixg.1011", responseObj.getString("message"));
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return dataJson;
    }
    
    public ReportCreateDTO doReport(Long userId, ReportCreatePO po) {
        Map<String, Object> body = new HashMap<>();
        body.put("userId", userId);
        body.put("topicType", po.getReportType());
        body.put("topic", po.getTopic());
        body.put("priceAssessmentReport", po.getPriceAssessmentReport());
        body.put("houses", po.getHouses());
        body.put("compare", po.getCompare());
        body.put("supportingFacilityReport", po.getSupportingFacilityReport());
        JSONObject responseObj = send(KerApiUrl.REPORT_CREATE.getTitle(), HttpMethod.POST, body);
        String data = responseObj.getString("data");
        JSONObject dataJson = JSONObject.parseObject(data);
        return new ReportCreateDTO().setReportId(dataJson.getString("reportId"));
    }

    public List<GoodsDTO> getGoods() {
        JSONObject responseObj = send(KerApiUrl.GOODS_LIST.getTitle(), HttpMethod.GET, null);
        String data = responseObj.getString("data");
        JSONArray jsonArray = JSONArray.parseArray(data);
        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .map(jsonObject1 -> new GoodsDTO()
                        .setId(jsonObject1.getLong("id"))
                        .setName(jsonObject1.getString("name"))
                        .setDesc(jsonObject1.getString("desc"))
                        .setOriginalPrice(jsonObject1.getBigDecimal("originalPrice"))
                        .setPrice(jsonObject1.getBigDecimal("price"))
                        .setExpireDays(jsonObject1.getInteger("expireDays"))
                        .setPoint(jsonObject1.getInteger("points"))
                ).collect(Collectors.toList());
    }

    public List<GoodsDTO> getVasGoods(Long kerId) {
        JSONObject responseObj = send(String.format(KerApiUrl.VAS_GOODS_LIST.getTitle(), kerId), HttpMethod.GET, null);
        String data = responseObj.getString("data");
        JSONArray jsonArray = JSONArray.parseArray(data);
        return jsonArray.stream()
                .map(obj -> (JSONObject) obj)
                .map(jsonObject1 -> new GoodsDTO()
                        .setId(jsonObject1.getLong("id"))
                        .setName(jsonObject1.getString("name"))
                        .setDesc(jsonObject1.getString("desc"))
                        .setOriginalPrice(jsonObject1.getBigDecimal("originalPrice"))
                        .setPrice(jsonObject1.getBigDecimal("price"))
                        .setExpireDays(jsonObject1.getInteger("expireDays"))
                        .setPoint(jsonObject1.getInteger("points"))
                ).collect(Collectors.toList());
    }

    public JSONObject createOrder(Long kerId, Long goodsId, String openId, String payCallbackUrl) {
        return send(KerApiUrl.CREATE_ORDER.getTitle(), HttpMethod.POST,
                Map.of(
                        "userId", kerId,
                        "goodsId", goodsId,
                        "payChannel", 13,
                        "openid", openId,
                        "payCallbackUrl", payCallbackUrl
                )
        ).getJSONObject("data");
    }

    public JSONObject queryOrder(Long kerId, String kerOrderNo) {
        return send(String.format(KerApiUrl.QUERY_ORDER.getTitle(), kerId, kerOrderNo), HttpMethod.GET, null);
    }
    
    @Async
    public SseEmitter buildSseEmitter(String url, Map<String, Object> body) {
        SseEmitter sseEmitter = new SseEmitter(Long.MAX_VALUE);
        String bodyStr = body != null ? JSON.toJSONString(body, true) : null;
        String accessToken = getToken();
    	Request request = new Request.Builder()
                .url(baseUrl + url)
                .header("Authorization", accessToken)
                .header("Content-Type", "application/json")
                .post(RequestBody.create(bodyStr, MediaType.parse("application/json")))
                .build();
        log.info("发送请求, bodyStr={}", bodyStr);
        log.info("发送请求, url={}, request={}", url, request);
        // 使用EventSourceListener处理来自服务器的SSE事件
        EventSourceListener listener = new EventSourceListener() {
            @Override
            public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                log.info("buildSseEmitter opened.");
            }
            @Override
            public void onClosed(@NotNull EventSource eventSource) {
                log.info("buildSseEmitter closed.");
                sseEmitter.complete();
            }
            @Override
            public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type, @NotNull String data) {
                try {
                    log.info("buildSseEmitter onEvent.id=" + id + ",type=" + type + ",data=" + data);
                    sendData(sseEmitter, data);
                    //sseEmitter.send(data);
                    //sseEmitter.send(SseEmitter.event().data(data));
                    //sseEmitter.send(SseEmitter.event().name("message").data(data));
                } catch (Exception e) {
                    log.error("推送数据失败", e);
                    sseEmitter.completeWithError(e); // 正确处理异常
                }
            }
            @Override
            public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
                log.error("buildSseEmitter failed.", t);
                sseEmitter.completeWithError(t);
            }
        };
        OkHttpClient client = new OkHttpClient.Builder().connectTimeout(10, TimeUnit.MINUTES).writeTimeout(50, TimeUnit.SECONDS).readTimeout(10, TimeUnit.MINUTES).build();
        EventSource.Factory factory = EventSources.createFactory(client);
        factory.newEventSource(request, listener);
        return sseEmitter;
    }
    
    public void sendData(SseEmitter emitter, String data) {
        try {
            emitter.send(data);
        } catch (IOException e) {
            emitter.completeWithError(e); // 发生错误时关闭连接
        }
    }

    private JSONObject send(String url, HttpMethod httpMethod, Object body) {

        String request = body != null ? JSON.toJSONString(body, true) : null;
        String accessToken = getToken();

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json;charset=UTF-8");
        headers.set("Authorization", accessToken);
        HttpEntity<String> entity = new HttpEntity<>(request, headers);
        log.info("发送请求, url={}, entity={}", url, entity);
        ResponseEntity<String> responseEntity = restTemplate.exchange(baseUrl + url, httpMethod, entity, String.class);
        String response = responseEntity.getBody();
        log.info("请求响应，response={}", response);
        JSONObject responseObj = JSONObject.parseObject(response, Feature.OrderedField);
        if (!"0".equals(responseObj.getString("code"))) {
        	log.info("请求克而瑞 " + baseUrl + url + "失败:" + responseObj.getString("message"));
            throw new BusinessException("bc.cpm.aixg.1014", responseObj.getString("message"));
        }
        return responseObj;
    }

    @Data
    public static class Token {
        private String accessToken;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        public Long expiresIn;
        private String tokenType;
    }
}
