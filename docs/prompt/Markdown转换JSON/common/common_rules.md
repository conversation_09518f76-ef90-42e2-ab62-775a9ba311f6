# 通用转换规范

## 核心转换原则

### 1. 内容忠实性原则（最高优先级）

**绝对忠实性要求**：严格基于原始markdown内容进行转换，绝对禁止添加、编造或推测任何不存在于原始文档中的信息。

**零虚构标准**：严禁生成任何虚假内容，包括但不限于：
- 原始文档中不存在的数据、数值、统计信息
- 原始文档中未提及的房源信息、价格、面积等
- 原始文档中没有的图表数据、对比信息
- 原始文档中缺失的分析结论、专业判断
- 任何基于推测或常识添加的"合理"内容

**内容完整性要求**：必须保留原始markdown中的每一个段落、每一句分析性内容，特别是：
- 数据解读段落（如"**数据解读**："后的所有分析内容）
- 专业分析文字（如市场洞察、趋势分析、专家观点等）
- 结论性陈述（如"体现出"、"表明"、"预示"等关键判断）
- 补充说明（如括号内的详细信息、注释内容等）

### 2. 严格数据来源验证原则

**数据来源追溯**：每个数据点都必须能够在原始markdown中找到明确的文字依据
- 数值数据：必须在原始文档的表格、列表或文字中明确存在
- 图表数据：只能使用原始文档中已有的完整数据集
- 房源信息：只能使用原始文档中明确提及的房源属性

**禁止数据推测**：严禁基于以下方式生成数据：
- 根据部分信息推算完整数据
- 基于常识或经验补充缺失数据
- 为了满足图表要求而编造数据点
- 通过逻辑推理生成"合理"的数值

### 3. 标题重复处理原则

**核心问题**：避免父子级控件之间出现相同标题内容，造成显示冗余

**处理策略**：
- **重复检测机制**：在生成每个LIST/TABLE/CHART控件时，自动检测其title是否与直接父级TITLE控件的title相同
- **智能省略规则**：当检测到标题重复时，子级控件的title字段应设置为空字符串或完全省略
- **结构优先原则**：保持父级TITLE控件的title不变，确保文档层次结构清晰可见

## 序列编号规范

### 编号层次结构
文档序列编号采用灵活的层次结构，支持根据内容需求选择合适的层次深度：

**完整层次结构**：
- **0级**：文档级别（TITLE控件的DOCUMENT样式，编号固定为"0"）
- **0.1级**：文章级别（TEXT控件的FLOAT样式，如引言、摘要、前言）
- **1级**：章节级别（TITLE控件的SECTION样式，编号为"1"、"2"、"3"等）
- **1.1级**：段落级别（TITLE控件的PARAGRAPH样式）
- **1.1.1级**：条目级别（TITLE控件的ENTRY样式）
- **1.1.1.1级**：其他控件级别（TEXT、LIST、CHART、TABLE等）

### 第一章节前控件编号规则
在报告内容中，第一章节前出现的所有控件均应遵循以下编号规则：

- **文档标题**：使用TITLE控件的DOCUMENT样式，编号固定为"0"
- **引言/摘要等**：使用TEXT控件的FLOAT样式，编号以"0."开头（如"0.1"、"0.2"、"0.3"等）
- **编号含义**：0级表示文档级别，0.x级表示文章级别的各个组成部分
- **层次逻辑**：文档标题 → 引言/摘要 → 正式章节，即 "0" → "0.1"、"0.2" → "1" → "1.1" → ...

## 数值处理规范

### 万单位转换规则
- **转换条件**：数值 ≥ 10000
- **转换方式**：除以10000，保留1-2位小数
- **数据类型**：转换后必须保持数字类型，不能包含"万"字符
- **单位标识**：在标题中添加单位说明（如"（万元）"、"（万套）"）

### 同图表单位一致性原则
**核心要求**：同一图表内所有数值必须使用相同单位格式

**决策逻辑**：
1. 评估图表内所有数值是否都适合转换
2. 只有全部适合时才统一转换为万单位
3. 否则全部保持原始单位

### JSON格式规范
**字符串转义**：
- 双引号：`"` → `\"`
- 反斜杠：`\` → `\\`
- 换行符：换行 → `\n`

**数值类型要求**：
- **图表数据**：必须为纯数字类型
  - 正确：`"content": 5.26`（数字类型）
  - 错误：`"content": "5.26万"`（字符串类型）
- **房源价格**：必须为字符串类型（包含单位）
  - 正确：`"price": "235万"`（字符串类型）
  - 错误：`"price": 235`（数字类型）

## 错误处理与容错机制

### 常见错误类型及处理
1. **格式不规范的markdown**：
   - 自动修复表格格式错误
   - 智能识别列表结构
   - 补全缺失的标题层级

2. **数据不完整**：
   - 严禁推断或估算缺失数据
   - 使用null值或省略相关字段
   - 绝不基于上下文补充数据

3. **结构混乱**：
   - 重新组织内容逻辑
   - 建立清晰的层级关系
   - 确保serial编号连续性

### 异常情况处理策略
- **数据冲突**：优先使用最新或最权威的数据
- **单位不统一**：按照一致性原则统一处理
- **内容过长**：合理分段，保持完整性
- **图表数据不足**：不生成该图表，严禁补充或编造数据

## 质量保证机制

### 输出验证标准
1. **结构完整性**：确保JSON结构符合DocumentData规范
2. **数据忠实性**：所有数据都能在原始markdown中找到明确来源
3. **格式规范性**：严格遵循各控件的格式要求
4. **内容完整性**：不遗漏原始markdown中的有价值信息，但绝不添加原文没有的内容
5. **来源可追溯性**：每个控件的内容都能追溯到原始文档的具体位置

### 强制验证机制
1. 转换完成后必须逐一验证每个数据点都能在原始文档中找到对应来源
2. 当原始文档缺少某些信息时，应省略该字段或使用空值，绝不编造
3. 所有生成的控件内容都必须有明确的原始文档依据
