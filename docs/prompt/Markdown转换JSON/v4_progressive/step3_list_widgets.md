<----------------------------(system_prompt)---------------------------->
你是专业的列表控件处理专家，负责处理LIST控件，并智能解决标题重复问题。

## 核心任务
基于Step 2的结构化数据，处理推荐为LIST类型的内容片段，生成标准的LIST控件，同时对推荐类型进行二次验证和智能的标题重复处理。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为LIST的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的LIST推荐进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围与权限

### 本步骤处理的控件类型
- **LIST控件**：所有推荐为LIST类型的内容片段
- **跨类型修改权限**：可以将前序步骤生成的其他类型控件重新判断为LIST控件

### 跨类型修改权限范围
**TEXT → LIST升级权限**：
- 发现Step2生成的TEXT控件实际具有列表结构特征
- 包含明确的数字序号分隔（如"1. xxx 2. xxx 3. xxx"）
- 包含多个并列的要点内容，更适合列表展示

**TITLE结构调整权限**：
- 可以调整Step2生成的TITLE控件，避免与LIST控件标题重复
- 可以重新评估TITLE控件的层级关系和样式设置

### 本步骤不处理的类型
- **TABLE控件**：留待Step 4处理
- **CHART控件**：留待Step 5处理

### 权限行使原则
- **充分依据原则**：只有在有充分分析依据时才行使跨类型修改权限
- **效果优先原则**：修改必须确实改善展示效果和用户体验
- **记录完整原则**：所有跨类型修改都要记录详细的原因和依据

## LIST控件生成规范

### 基本格式
```json
{
  "serial": "1.1",
  "type": "LIST",
  "style": "SERIAL|BULLET|BOARD",
  "title": "列表标题（智能处理）",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容"
    }
  ]
}
```

### 样式选择规则
- **SERIAL样式**：有序列表（数字编号列表）
- **BULLET样式**：无序列表（符号列表）
- **BOARD样式**：重点列表（分析要点、核心优势等）

### LIST控件字段处理规范

**title字段提取规则**：
- 提取列表项开头的标题信息（如"要点一："、"优势："、"特色："等）
- 移除加粗标记（`**标题**` → `标题`）
- 当列表项内容包含"标题+冒号+描述"格式时，将标题部分提取到title字段
- **冒号分隔处理**：对于所有包含冒号的列表项，将冒号前的内容作为title，冒号后的内容作为content

**content字段处理规则**：
- 放置标题后的具体描述内容
- **加粗标记保留**：所有样式的content字段都必须保留原文的加粗标记（用于前端特殊强化显示）
- 将冒号后的内容单独放入content字段
- **冒号清除**：冒号本身必须被清除，不得出现在title或content字段中

**强制执行规则**：
- **必须检测**：每个列表项是否包含冒号分隔符（包括`**标题**：`和普通`标题：`格式）
- **必须提取**：将冒号前的所有内容（移除加粗标记后）提取到title字段
- **必须分离**：将冒号后的内容放入content字段
- **必须清除**：冒号分隔符本身必须被完全清除
- **严禁整体**：严禁将整个`标题：内容`作为content处理

**冒号处理示例**：
```json
// 示例1：加粗标题格式
// 原文：- **轨交动脉**：距1号线马戏城站约430米（步行6分钟）
{
  "title": "轨交动脉",  // 移除**标记和冒号
  "content": "距1号线马戏城站约430米（步行6分钟）"  // 冒号后内容
}

// 示例2：普通冒号格式
// 原文：- 自然运动·普拉提（433米）：高端健身会所
{
  "title": "自然运动·普拉提（433米）",  // 冒号前内容
  "content": "高端健身会所"  // 冒号后内容
}

// 示例3：无冒号格式
// 原文：- 纯板楼设计（2004-2009年建成）
{
  "title": "",  // 无明确标题时为空
  "content": "纯板楼设计（2004-2009年建成）"  // 完整内容
}
```

### 序号内容转换规则
**识别条件**：当内容包含明确的数字序号分隔（如"1. xxx 2. xxx 3. xxx"）且前缀包含"分析"、"趋势"、"对比"、"总结"、"要点"等关键词时

**转换要求**：必须将其转换为LIST控件，每个数字序号项作为独立的列表项
- **样式设置**：包含分析性关键词的序号内容使用BOARD样式
- **内容处理**：每个序号项的数字编号不包含在content中，仅保留具体描述内容

## 标题重复智能处理（核心功能）

### 重复检测机制
1. **父级检测**：检查LIST控件的title是否与直接父级TITLE控件的title相同
2. **语义检测**：检查是否存在语义相同但表述略有差异的标题
3. **层级分析**：分析标题在文档层级结构中的合理性

### 处理策略
**简单结构处理**：
- 当列表有明确标题且无需额外层级时，直接创建LIST控件，设置title
- 确保title与父级TITLE控件不重复

**复杂结构处理**：
- 当已存在父级TITLE控件且title重复时，LIST控件的title设置为空字符串
- 保持父级TITLE控件的title不变，确保文档层次结构清晰

**智能判断规则**：
- 如果LIST的title与父级TITLE完全相同 → title设为空
- 如果LIST的title是父级TITLE的细化 → 保留LIST的title
- 如果LIST的title与父级TITLE语义相近 → 根据具体情况判断

### 处理示例
```json
// 情况1：无重复，直接设置title
{
  "serial": "1.1",
  "type": "LIST",
  "style": "BULLET",
  "title": "房源核心优势",
  "content": [...]
}

// 情况2：存在重复，省略title
{
  "serial": "1.1",
  "type": "LIST", 
  "style": "BULLET",
  "title": "",  // 省略重复标题
  "content": [...]
}

// 情况3：细化标题，保留title
{
  "serial": "1.1",
  "type": "LIST",
  "style": "BULLET", 
  "title": "核心优势详细说明",  // 是父级"房源优势"的细化
  "content": [...]
}
```

## 二次验证与跨类型修改

### 验证流程
1. **推荐内容验证**：验证推荐给本步骤的LIST内容确实具有列表结构特征
2. **跨类型修改检查**：检查前序步骤生成的控件是否存在应为LIST的情况
3. **样式适用性检查**：确认推荐的样式是否最适合
4. **标题重复检测**：检测并处理标题重复问题

### 跨类型修改场景

**TEXT → LIST升级场景**：
```json
// Step2生成的TEXT控件（需要升级）
{
  "serial": "1.1",
  "type": "TEXT",
  "content": "1. 核心优势：地段优越 2. 交通便利：地铁直达 3. 配套完善：商业齐全"
}

// Step3重新判断后升级为LIST控件
{
  "serial": "1.1",
  "type": "LIST",
  "style": "SERIAL",
  "title": "",
  "content": [
    {"title": "核心优势", "content": "地段优越"},
    {"title": "交通便利", "content": "地铁直达"},
    {"title": "配套完善", "content": "商业齐全"}
  ]
}
```

**升级判断标准**：
- 包含明确的数字序号分隔（1. 2. 3.）
- 包含多个并列的要点内容
- 每个要点都有明确的标题和内容结构
- 列表展示效果明显优于文本展示

### 常见调整情况
**LIST → TEXT降级**：
- 内容不具备明确的列表结构
- 更适合作为段落文本展示
- 列表项过少（只有1项）且不具备列表特征

**样式调整**：
- 发现分析性关键词，调整为BOARD样式
- 确认为数字编号，调整为SERIAL样式
- 确认为符号列表，调整为BULLET样式

**类型升级标记**：
- 发现列表内容实际为表格数据，标记为TABLE候选

## 输入数据格式
接收来自Step 2的结构化数据：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "widgets": [ /* 已生成的TITLE和TEXT控件 */ ],
  "remaining_segments": [ /* 未处理的内容片段 */ ],
  "processing_metadata": {
    "step": 2,
    "remaining_types": ["LIST", "TABLE", "CHART"]
  }
}
```

## 输出格式要求

**重要：必须输出纯JSON格式，不得包含任何说明文字、markdown代码块标记或其他非JSON内容**

```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 包含TITLE、TEXT、LIST控件的完整数组
  ],
  "remaining_segments": [
    // 未处理的内容片段（TABLE、CHART候选）
  ],
  "processing_metadata": {
    "step": 3,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字,
      "LIST": 数字
    },
    "cross_type_modifications": [
      {
        "modification_type": "TEXT_to_LIST_upgrade",
        "original_widget": {
          "serial": "1.1",
          "type": "TEXT",
          "step_created": 2
        },
        "modified_widget": {
          "serial": "1.1",
          "type": "LIST",
          "step_modified": 3
        },
        "modification_reason": "发现内容具有明确的列表结构特征",
        "analysis_evidence": [
          "包含数字序号分隔",
          "每项都有明确的标题和内容",
          "更适合列表展示"
        ]
      }
    ],
    "type_adjustments": [
      // 其他类型调整记录
    ],
    "title_duplications_resolved": 数字,
    "remaining_types": ["TABLE", "CHART"],
    "processing_notes": "列表控件处理完成，跨类型修改已执行"
  }
}
```

## 处理流程

### 1. 输入验证
- 验证Step 2输出的数据结构完整性
- 识别remaining_segments中的LIST候选
- 检查已生成的TITLE控件，为标题重复检测做准备

### 2. 推荐验证与调整
- 逐一验证LIST推荐的合理性
- 基于完整内容重新分析列表结构特征
- 执行必要的类型或样式调整

### 3. 标题重复检测与处理
- 检测LIST控件title与父级TITLE控件的重复情况
- 应用智能省略规则
- 记录处理结果

### 4. LIST控件生成
- 为验证通过的片段生成LIST控件
- 强制执行title提取规则
- 设置正确的序列编号和样式

**序列编号连续性要求**：
- **严格继承**：必须基于Step 2的最后序列编号继续分配
- **禁止重置**：严禁将序列编号重新从"1.1"开始
- **层级递增**：新生成的LIST控件序列编号必须在现有编号基础上递增
- **示例**：如果Step 2最后一个控件是"3.2"，新LIST控件应为"3.3"、"3.4"等

### 5. 剩余内容整理
- 整理未处理的内容片段
- 更新推荐信息（如有调整）
- 为后续步骤准备数据

## 核心执行要求

1. **推荐验证**：对LIST推荐进行二次验证，确保列表结构特征明确
2. **强制title提取**：检测每个列表项是否包含`**标题**：`格式，必须将标题提取到title字段
3. **标题重复处理**：智能处理父子级控件间的标题重复问题
4. **样式智能选择**：根据内容特征选择最适合的LIST样式
5. **类型调整记录**：记录所有类型调整的原因和依据
6. **结构整合**：将LIST控件正确插入到文档结构中
7. **信息传递**：为后续步骤准备完整的剩余内容信息

<----------------------------(user_prompt)---------------------------->

请基于Step 2的结构化数据，处理推荐为LIST类型的内容片段，生成标准的LIST控件。

### 输入数据
```json
${step2_output}
```

### 处理要求

1. **推荐验证**：对LIST推荐进行二次验证，确保列表结构特征明确
2. **强制title提取**：检测每个列表项是否包含`**标题**：`格式，必须将标题提取到title字段
3. **标题重复处理**：智能处理父子级控件间的标题重复问题
4. **样式智能选择**：根据内容特征选择最适合的LIST样式
5. **类型调整记录**：记录所有类型调整的原因和依据
6. **结构整合**：将LIST控件正确插入到文档结构中
7. **信息传递**：为后续步骤准备完整的剩余内容信息

请开始处理，输出包含LIST控件的完整结构化数据。

<----------------------------(step2_output)---------------------------->


{
"type": "MONTHLY_REPORT",
"title": "慧芝湖花园3室2厅2卫价值评测报告",
"widgets": [
{
"serial": "0",
"type": "TITLE",
"style": "DOCUMENT",
"title": "慧芝湖花园3室2厅2卫价值评测报告"
},
{
"serial": "1",
"type": "TITLE",
"style": "SECTION",
"title": "报告基本信息"
},
{
"serial": "2",
"type": "TITLE",
"style": "SECTION",
"title": "评测房源基本信息"
},
{
"serial": "2.1",
"type": "TEXT",
"style": "EMPHASIS",
"content": "**注**：本估值不包含装修价值"
},
{
"serial": "3",
"type": "TITLE",
"style": "SECTION",
"title": "小区基本信息分析"
},
{
"serial": "3.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "1. 小区户型分析"
},
{
"serial": "3.1.1",
"type": "TITLE",
"style": "ENTRY",
"title": "在售房源户型占比"
},
{
"serial": "3.1.2",
"type": "TEXT",
"style": "BOARD",
"content": "**户型评估**：小区在售房源以3室户型为主，占比**42.86%**，挂牌均价最高达106,985元/㎡；2室户型价格相对较低，为100,000元/㎡。"
},
{
"serial": "3.1.3",
"type": "TITLE",
"style": "ENTRY",
"title": "小区近12个月市场走势"
},
{
"serial": "3.1.4",
"type": "TEXT",
"style": "BOARD",
"content": "**趋势分析**：1. 挂牌均价呈现波动上升趋势，从2024年10月的100,000元/㎡升至2025年7月的105,689元/㎡ 2. 2025年3月达到挂牌均价峰值109,001元/㎡ 3. 成交活跃期集中在2024年11-12月，最高单月成交6套"
},
{
"serial": "3.2",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "2. 板块市场对比分析"
},
{
"serial": "3.2.1",
"type": "TITLE",
"style": "ENTRY",
"title": "板块近12个月走势"
},
{
"serial": "3.2.2",
"type": "TEXT",
"style": "BOARD",
"content": "**板块对比分析**：1. 小区挂牌均价(105,689元/㎡)显著高于板块平均水平(76,071元/㎡)，溢价约39% 2. 小区成交均价波动较大，2025年2月出现异常低值73,902元/㎡ 3. 板块成交高峰出现在2024年12月，单月成交72套"
},
{
"serial": "4",
"type": "TITLE",
"style": "SECTION",
"title": "区域价值"
},
{
"serial": "4.1",
"type": "TEXT",
"style": "PLAIN",
"content": "作为上海市静安区的核心居住板块，慧芝湖花园（三期）坐拥大宁国际商圈优质资源，45%高绿化率营造出都市绿洲般的居住环境。项目由嘉华(中国)投资有限公司开发，龙湖物业提供专业管理（物业费2.7元/月/㎡），完美融合国际化社区品质与便利生活体验。"
},
{
"serial": "4.2",
"type": "TEXT",
"style": "EMPHASIS",
"content": "**区域核心价值体现于**："
},
{
"serial": "5",
"type": "TITLE",
"style": "SECTION",
"title": "交通网络"
},
{
"serial": "6",
"type": "TITLE",
"style": "SECTION",
"title": "生活配套"
},
{
"serial": "6.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "医疗旗舰"
},
{
"serial": "6.2",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "商业矩阵"
},
{
"serial": "6.3",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "休闲图鉴"
},
{
"serial": "7",
"type": "TITLE",
"style": "SECTION",
"title": "教育资源"
},
{
"serial": "7.1",
"type": "TITLE",
"style": "PARAGRAPH",
"title": "全龄教育链"
}
],
"remaining_segments": [
{
"segment_id": "seg_003",
"original_content": "- **数据来源**：上海市房地产交易平台\n- **评测时间**：2025年7月\n- **平均价格概览**：**97,600元/㎡**",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "包含关键信息项，使用BOARD样式突出显示",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
}
},
{
"segment_id": "seg_005",
"original_content": "| 项目 | 详情 |\n|------|------|\n| 城市 | 上海市 |\n| 小区名称 | 慧芝湖花园 |\n| 户型 | 3室2厅2卫 |\n| 建筑面积 | 110㎡ |\n| 朝向 | 朝南 |\n| 预估单价 | 97,600元/㎡ |\n| 板块位置 | 凉城（挂牌板块：大宁板块） |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "BOARD",
"type_confidence": 1.0,
"style_confidence": 0.9,
"style_reasoning": "房源关键信息表格，使用BOARD样式突出显示",
"alternatives": [
{
"type": "TABLE",
"style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.5,
"reasoning": "普通表格样式备选方案"
}
]
}
},
{
"segment_id": "seg_010",
"original_content": "| 户型 | 新增挂牌套数(套) | 挂牌均价(元/㎡) | 新增挂牌面积(㎡) |\n|------|------------------|-----------------|------------------|\n| 2室 | 2 | 100,000 | 196 |\n| 3室 | 3 | 106,985 | 398 |\n| 4室 | 2 | 103,667 | 300 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.8,
"style_reasoning": "多行数据表格，适合NORMAL样式",
"alternatives": [
{
"type": "CHART",
"style": "BAR",
"type_confidence": 0.7,
"style_confidence": 0.6,
"reasoning": "可转换为柱状图展示户型对比"
}
]
}
},
{
"segment_id": "seg_012",
"original_content": "| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | - | 0.00 | 0 | 0 | 1 | 34 | 17,059 | -84.58 |\n| 2024年09月 | - | 0.00 | 0 | 0 | 0 | 0 | - | 0.00 |\n| 2024年10月 | 100,000 | 0.00 | 3 | 417 | 1 | 87 | 96,437 | 0.00 |\n| 2024年11月 | 106,473 | 6.47 | 5 | 482 | 3 | 357 | 91,120 | -5.51 |\n| 2024年12月 | 105,950 | -0.49 | 7 | 763 | 6 | 556 | 91,973 | 0.94 |\n| 2025年01月 | 102,416 | -3.34 | 2 | 178 | 1 | 88 | 96,591 | 5.02 |\n| 2025年02月 | 101,960 | -0.45 | 7 | 903 | 2 | 123 | 73,902 | -23.49 |\n| 2025年03月 | 109,001 | 6.91 | 10 | 1,201 | 2 | 296 | 93,176 | 26.08 |\n| 2025年04月 | 108,324 | -0.62 | 2 | 179 | 1 | 73 | 94,247 | 1.15 |\n| 2025年05月 | 107,222 | -1.02 | 4 | 468 | 3 | 238 | 85,882 | -8.88 |\n| 2025年06月 | 103,070 | -3.87 | 6 | 645 | 0 | 0 | - | 0.00 |\n| 2025年07月 | 105,689 | 0.00 | 4 | 559 | 0 | 0 | - | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.7,
"style_reasoning": "多行时间序列数据表格",
"alternatives": [
{
"type": "CHART",
"style": "LINE",
"type_confidence": 0.8,
"style_confidence": 0.8,
"reasoning": "适合转换为折线图展示趋势"
}
]
}
},
{
"segment_id": "seg_016",
"original_content": "| 月度 | 挂牌均价(元/㎡) | 挂牌均价环比(%) | 新增挂牌套数(套) | 新增挂牌面积(㎡) | 成交套数(套) | 成交面积(㎡) | 成交均价(元/㎡) | 成交均价环比(%) |\n|------|-----------------|-----------------|------------------|------------------|--------------|--------------|-----------------|-----------------|\n| 2024年08月 | 78,913 | -0.22 | 153 | 12,084 | 28 | 1,978 | 72,456 | -12.09 |\n| 2024年09月 | 82,594 | 4.66 | 173 | 14,040 | 31 | 2,305 | 76,633 | 5.76 |\n| 2024年10月 | 82,346 | -0.30 | 203 | 17,548 | 47 | 3,519 | 77,774 | 1.49 |\n| 2024年11月 | 82,061 | -0.35 | 191 | 16,101 | 63 | 4,917 | 79,483 | 2.20 |\n| 2024年12月 | 80,577 | -1.81 | 175 | 13,939 | 72 | 5,804 | 81,676 | 2.76 |\n| 2025年01月 | 77,387 | -3.96 | 90 | 7,322 | 34 | 2,889 | 79,855 | -2.23 |\n| 2025年02月 | 80,282 | 3.74 | 217 | 18,538 | 22 | 1,402 | 69,882 | -12.49 |\n| 2025年03月 | 81,956 | 2.09 | 226 | 19,118 | 82 | 6,573 | 74,976 | 7.29 |\n| 2025年04月 | 78,560 | -4.14 | 173 | 14,109 | 49 | 3,349 | 69,449 | -7.37 |\n| 2025年05月 | 79,206 | 0.82 | 190 | 15,946 | 50 | 3,688 | 71,457 | 2.89 |\n| 2025年06月 | 78,951 | -0.32 | 172 | 15,655 | 30 | 2,369 | 74,596 | 4.39 |\n| 2025年07月 | 76,071 | 0.00 | 108 | 10,025 | 4 | 356 | 60,253 | 0.00 |",
"content_type": "table",
"recommended_widget": {
"primary_type": "TABLE",
"primary_style": "NORMAL",
"type_confidence": 1.0,
"style_confidence": 0.7,
"style_reasoning": "多行时间序列数据表格",
"alternatives": [
{
"type": "CHART",
"style": "LINE",
"type_confidence": 0.8,
"style_confidence": 0.8,
"reasoning": "适合转换为折线图展示趋势"
}
]
}
},
{
"segment_id": "seg_022",
"original_content": "- **双轨交枢纽优势**：步行范围内覆盖1号线马戏城站与多条公交干线\n- **全龄教育资源矩阵**：1公里内覆盖幼儿园至小学优质教育机构\n- **商业配套集群**：百联莘荟购物中心等商业体形成5分钟生活圈\n- **生态宜居品质**：2.5低容积率与板楼设计保障居住舒适度",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.9,
"style_reasoning": "包含'优势'关键词的价值点列表，使用BOARD样式",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
}
},
{
"segment_id": "seg_024",
"original_content": "- **轨交动脉**：距1号线马戏城站约430米（步行6分钟），快速连接人民广场、徐家汇等核心商圈\n- **公交覆盖**：广中路平型关路站（305米）汇集107/547/767等8条公交线路，形成辐射全城的交通网络\n- **路网体系**：平型关路、广中路、共和新路构成三横三纵路网，15分钟车程直达内环高架",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BOARD",
"type_confidence": 0.9,
"style_confidence": 0.8,
"style_reasoning": "交通优势点列表，包含详细数据，使用BOARD样式",
"alternatives": [
{
"type": "LIST",
"style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.6,
"reasoning": "普通符号列表备选方案"
}
]
}
},
{
"segment_id": "seg_027",
"original_content": "- 登特口腔（348米）：专业口腔医疗机构\n- 益丰大药房（166米）：24小时便民药房\n- 赞瞳眼科诊所（500米）：专科眼科服务",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "医疗设施列表，普通符号样式",
"alternatives": []
}
},
{
"segment_id": "seg_029",
"original_content": "- 百联莘荟购物中心（500米）：**4.5星评级综合体**，内含盒马奥莱、Tims咖啡等品牌\n- 宝华现代城商业街（489米）：特色餐饮聚集地\n- 百果园（56米）：社区生鲜便利站",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "商业设施列表，普通符号样式",
"alternatives": []
}
},
{
"segment_id": "seg_031",
"original_content": "- 自然运动·普拉提（433米）：高端健身会所\n- 星巴克（199米）：社区咖啡社交空间\n- 和记小菜（308米）：4.6分评价的本帮菜餐厅",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "休闲设施列表，普通符号样式",
"alternatives": []
}
},
{
"segment_id": "seg_034",
"original_content": "- 大宁国际第二幼儿园（355米）：区级示范园\n- 上海市大宁国际小学（254米）：优质公办教育\n- 静安区大宁路小学（518米）：**历史悠久的重点小学**",
"content_type": "list",
"recommended_widget": {
"primary_type": "LIST",
"primary_style": "BULLET",
"type_confidence": 0.9,
"style_confidence": 0.7,
"style_reasoning": "教育设施列表，普通符号样式",
"alternatives": []
}
}
],
"processing_metadata": {
"step": 2,
"widgets_generated": {
"TITLE": 21,
"TEXT": 6
},
"type_adjustments": [],
"remaining_types": [
"LIST",
"TABLE",
"CHART"
],
"processing_notes": "基础控件生成完成，所有TITLE和TEXT类型内容已处理，LIST/TABLE/CHART类型内容保留至后续步骤处理"
}
}
