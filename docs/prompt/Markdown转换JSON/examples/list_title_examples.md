# 列表标题处理示例

## 示例1：简单结构处理

**原始markdown结构**：
```markdown
#### 房源核心优势
- **南北通透，采光通风俱佳**：110㎡方正户型，得房率高...
- **低楼层，安静宜居**：未临近主干道，实测噪音低...
```

**正确处理方式**：
```json
{
  "serial": "1.1",
  "type": "LIST",
  "style": "ITEM",
  "title": "房源核心优势",
  "content": [
    {
      "title": "南北通透，采光通风俱佳",
      "content": "110㎡方正户型，得房率高，实际使用面积充足..."
    },
    {
      "title": "低楼层，安静宜居", 
      "content": "未临近主干道，实测噪音低..."
    }
  ]
}
```

## 示例2：复杂结构处理（标题重复）

**原始markdown结构**：
```markdown
## 房源核心优势

#### 房源核心优势
- **南北通透，采光通风俱佳**：110㎡方正户型...
- **低楼层，安静宜居**：未临近主干道...
```

**正确处理方式**：
```json
// 父级TITLE控件
{
  "serial": "1",
  "type": "TITLE",
  "style": "SECTION",
  "title": "房源核心优势"
},
// 子级LIST控件（title省略）
{
  "serial": "1.1",
  "type": "LIST",
  "style": "ITEM",
  "title": "",  // 省略重复标题
  "content": [
    {
      "title": "南北通透，采光通风俱佳",
      "content": "110㎡方正户型，得房率高..."
    },
    {
      "title": "低楼层，安静宜居",
      "content": "未临近主干道，实测噪音低..."
    }
  ]
}
```

## 示例3：**标题：**格式处理

**原始markdown结构**：
```markdown
**交通与配套：**
- 地铁1号线大宁路站，步行约8分钟
- 周边商业配套成熟，生活便利
```

**正确处理方式**：
```json
{
  "serial": "2.1",
  "type": "LIST",
  "style": "ITEM",
  "title": "交通与配套",
  "content": [
    {
      "title": "地铁交通",
      "content": "地铁1号线大宁路站，步行约8分钟"
    },
    {
      "title": "商业配套",
      "content": "周边商业配套成熟，生活便利"
    }
  ]
}
```

## 错误处理示例

**错误方式1：创建冗余TITLE控件**
```json
// 错误：同时创建TITLE控件和带相同title的LIST控件
{
  "serial": "1.1",
  "type": "TITLE",
  "style": "PARAGRAPH", 
  "title": "房源核心优势"
},
{
  "serial": "1.1.1",
  "type": "LIST",
  "style": "ITEM",
  "title": "房源核心优势",  // 错误：重复标题
  "content": [...]
}
```

**错误方式2：LIST控件缺少title**
```json
// 错误：明明有列表标题却不设置title字段
{
  "serial": "1.1",
  "type": "LIST",
  "style": "ITEM",
  // 错误：缺少title字段
  "content": [...]
}
```

**错误方式3：使用字符串数组**
```json
// 错误：content使用字符串数组而非对象数组
{
  "serial": "1.1",
  "type": "LIST",
  "style": "ITEM",
  "title": "房源核心优势",
  "content": [
    "南北通透，采光通风俱佳：110㎡方正户型...",  // 错误格式
    "低楼层，安静宜居：未临近主干道..."  // 错误格式
  ]
}
```
