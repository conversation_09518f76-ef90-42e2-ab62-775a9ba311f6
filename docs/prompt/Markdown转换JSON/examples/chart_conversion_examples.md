# 图表转换示例

## 示例1：PIE图转换

**原始表格数据**：
```markdown
| 户型  | 占比 |
|-------|------|
| 2室   | 35%  |
| 3室   | 45%  |
| 4室   | 20%  |
```

**转换为PIE图**：
```json
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "户型分布",
  "content": [
    {
      "title": "2室",
      "content": 35
    },
    {
      "title": "3室", 
      "content": 45
    },
    {
      "title": "4室",
      "content": 20
    }
  ]
}
```

## 示例2：BAR图转换（万单位处理）

**原始表格数据**：
```markdown
| 月度       | 挂牌均价(元/㎡) | 成交均价(元/㎡) |
|------------|------------------|------------------|
| 2024年11月 | 106,473          | 91,120           |
| 2024年12月 | 105,950          | 91,973           |
| 2025年03月 | 109,001          | 93,176           |
```

**转换为BAR图**：
```json
{
  "serial": "2.2",
  "type": "CHART",
  "style": "BAR",
  "title": "月度价格对比（万元/㎡）",
  "cols": ["2024年11月", "2024年12月", "2025年03月"],
  "content": [
    {
      "title": "挂牌均价",
      "content": [10.65, 10.60, 10.90]
    },
    {
      "title": "成交均价",
      "content": [9.11, 9.20, 9.32]
    }
  ]
}
```

## 示例3：LINE图转BAR图（数据不连续处理）

**原始表格数据**（存在大量null值）：
```markdown
| 月度       | 成交套数 |
|------------|----------|
| 2024年11月 | 3        |
| 2024年12月 | 6        |
| 2025年01月 | -        |
| 2025年02月 | -        |
| 2025年03月 | 2        |
| 2025年04月 | -        |
| 2025年05月 | -        |
| 2025年06月 | -        |
| 2025年07月 | -        |
```

**数据连续性分析**：
- 总数据点：9个
- null值数量：6个
- null值占比：6/9 = 66.7% > 50%
- **结论**：数据不连续，应使用BAR图而非LINE图

**转换为BAR图**：
```json
{
  "serial": "2.3",
  "type": "CHART",
  "style": "BAR",
  "title": "月度成交套数",
  "cols": ["2024年11月", "2024年12月", "2025年03月"],
  "content": [
    {
      "title": "成交套数",
      "content": [3, 6, 2]
    }
  ]
}
```

## 示例4：量级差异处理（拆分图表）

**原始表格数据**：
```markdown
| 指标           | 数值      |
|----------------|-----------|
| 挂牌均价(元/㎡) | 106,473   |
| 成交套数       | 3         |
| 板块均价(元/㎡) | 76,071    |
| 板块成交套数   | 31        |
```

**量级差异分析**：
- 价格数据：10万级别
- 套数数据：个位数级别
- 比值：106,473/3 ≈ 35,491:1 > 100:1
- **结论**：量级差异过大，需要拆分

**拆分为两个图表**：

**图表1：价格对比**
```json
{
  "serial": "2.4",
  "type": "CHART",
  "style": "BAR",
  "title": "价格对比（万元/㎡）",
  "cols": ["小区", "板块"],
  "content": [
    {
      "title": "均价对比",
      "content": [10.65, 7.61]
    }
  ]
}
```

**图表2：成交量对比**
```json
{
  "serial": "2.5",
  "type": "CHART",
  "style": "BAR",
  "title": "成交套数对比",
  "cols": ["小区", "板块"],
  "content": [
    {
      "title": "成交套数",
      "content": [3, 31]
    }
  ]
}
```

## 示例5：同图表单位一致性处理

**原始表格数据**：
```markdown
| 项目     | 数值    |
|----------|---------|
| 总价     | 235万   |
| 单价     | 50,000  |
| 面积     | 110     |
```

**单位一致性分析**：
- 总价：万级别（235万）
- 单价：万级别（5万元/㎡）
- 面积：百级别（110㎡）
- **结论**：面积数据不适合万单位转换，保持原始单位

**转换结果**：
```json
{
  "serial": "2.6",
  "type": "CHART",
  "style": "BAR",
  "title": "房源基本信息",
  "cols": ["总价(万)", "单价(元/㎡)", "面积(㎡)"],
  "content": [
    {
      "title": "房源数据",
      "content": [235, 50000, 110]
    }
  ]
}
```
