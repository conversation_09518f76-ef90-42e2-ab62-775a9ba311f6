# Markdown转换JSON v3.0 - 分步处理方案

## 概述

本方案将原来的单步骤提示词拆分为5个专门化的步骤，每个步骤专注于特定的转换任务，有效控制了提示词长度并提升了处理质量。

## 方案架构

### 公共规范文档
- `common/common_rules.md` - 通用转换原则和规范
- `common/widget_specs.md` - 控件规范说明
- `examples/` - 处理示例和最佳实践

### 5步处理流程（优化版）

#### Step 1: 结构识别与基础转换 (200行)
- **文件**: `step1_structure_optimized.md`
- **职责**: markdown结构解析、TITLE和TEXT控件生成
- **输出**: 基础JSON结构 + 处理元数据
- **重点**: 文档框架建立、分析性内容识别、LIST控件规范集成
- **优化**: 集成LIST控件处理规则、加粗标记处理、数据排序要求

#### Step 2: 列表与标题处理 (220行)
- **文件**: `step2_list_optimized.md`
- **职责**: LIST控件生成、标题重复智能处理
- **输出**: 包含LIST控件的完整结构
- **重点**: 标题重复检测与解决、强制title提取规则
- **优化**: 完整的LIST控件处理规范、序号内容转换规则

#### Step 3: 数据表格处理 (230行)
- **文件**: `step3_table_optimized.md`
- **职责**: TABLE控件生成、图表候选标记
- **输出**: 包含TABLE控件的结构 + 图表候选信息
- **重点**: 数据准确性、recommended属性应用、图表转换准备
- **优化**: 集成recommended属性规则、数据排序要求

#### Step 4: 图表智能转换 (240行)
- **文件**: `step4_chart_optimized.md`
- **职责**: TABLE→CHART转换、数据可视化优化
- **输出**: 包含优化图表的完整结构
- **重点**: 智能类型选择、数据连续性处理、量级差异处理
- **优化**: 完整的数据连续性算法、量级差异检测与拆分策略

#### Step 5: 数据验证与最终优化 (200行)
- **文件**: `step5_validation_optimized.md`
- **职责**: 质量检查、忠实性验证、最终优化
- **输出**: 符合规范的最终DocumentData JSON
- **重点**: 全面质量保证、精简验证清单
- **优化**: 精简检查清单、重点突出核心验证项

## 使用方式

### 顺序执行模式
```
原始markdown → Step1 → Step2 → Step3 → Step4 → Step5 → 最终JSON
```

### 数据传递格式
每个步骤通过`processing_metadata`传递处理信息：
```json
{
  "step": 步骤编号,
  "处理统计": "各种计数信息",
  "候选标记": ["待处理项目列表"],
  "processing_notes": "处理说明"
}
```

## 核心优势

### 1. 长度有效控制
- 每个步骤提示词控制在200-240行
- 自包含设计，无需外部文件引用
- 核心规范内联嵌入，确保可用性

### 2. 专业化分工
- 每个步骤专注特定任务
- 减少任务冲突和复杂度
- 提升处理质量和准确性

### 3. 质量保证优化
- 分步验证，逐层质量控制
- 精简检查清单，重点突出核心验证
- 忠实性检查贯穿全流程

### 4. 实用性优先
- 解决了引用文件无法使用的问题
- 每个提示词都是完整可用的
- 减少了无效的重复检查

### 5. 内容完整性
- 集成了老提示词中的所有重要规则
- LIST控件处理规则完整保留
- recommended属性、数据排序等规范完整集成

## 关键改进点

### 1. 解决引用文件问题
- 采用内联嵌入方案，每个提示词自包含
- 不再依赖外部文件引用，确保实际可用性
- 核心规范直接集成到各步骤中

### 2. 补充遗漏内容
- 完整集成LIST控件处理规则（title提取、content分离等）
- 集成加粗标记处理规则和数据排序要求
- 补充recommended属性应用规则和序号内容转换

### 3. 优化检查机制
- 精简检查清单，从20+项减少到4-6项核心检查
- 将检查要求集成到执行步骤中，避免检查疲劳
- 重点突出最关键的验证项

### 4. 标题重复处理
- Step 2专门处理父子级控件标题重复问题
- 智能检测与省略机制
- 确保文档结构清晰

### 5. 图表智能转换
- Step 4专门处理图表转换优化
- 数据连续性智能检测（null值占比算法）
- 量级差异自动处理（100:1阈值检测）

### 6. 分析内容突出
- Step 1重点识别分析性内容
- BOARD样式突出显示
- 确保重要内容不遗漏

### 7. 数据忠实性
- 每个步骤都强调忠实性原则
- Step 5进行最终忠实性验证
- 绝不添加原文不存在的内容

## 质量控制机制

### 分层验证
- 每个步骤内部质量检查
- 步骤间数据传递验证
- 最终步骤全面质量保证

### 忠实性保证
- 数据来源追溯验证
- 内容完整性检查
- 虚构内容零容忍

### 格式规范
- 严格遵循控件规范
- JSON格式标准化
- 数据类型准确性

## 使用建议

### 1. 严格按顺序执行
- 不要跳过任何步骤
- 确保数据传递完整
- 验证每步输出质量

### 2. 重视忠实性验证
- 特别关注Step 5的验证结果
- 发现问题及时回溯修正
- 宁缺毋滥，确保质量

### 3. 灵活应用规范
- 根据文档复杂度选择层级深度
- 合理使用控件样式
- 注重用户体验

### 4. 持续优化改进
- 收集处理反馈
- 优化提示词内容
- 更新示例和规范

## 注意事项

1. **数据传递完整性**: 确保每个步骤的输出包含完整的processing_metadata
2. **忠实性优先**: 任何情况下都不能违背内容忠实性原则
3. **格式严格性**: 严格遵循各控件的格式规范要求
4. **质量检查**: 每个步骤都要进行相应的质量检查
5. **错误处理**: 发现问题时优先修复而非删除内容

通过这种分步处理方案，我们有效解决了原有提示词过长的问题，同时提升了转换质量和维护便利性。
