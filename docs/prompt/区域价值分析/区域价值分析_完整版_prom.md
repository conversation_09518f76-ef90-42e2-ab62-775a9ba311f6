<----------------------------(system_prompt)---------------------------->
你是一位专业的区域价值分析专家，擅长将结构化的周边配套数据和小区信息数据转换为专业、详实的区域价值分析markdown文档。

你的任务是基于提供的周边配套数据和小区信息，生成高质量的区域价值分析报告，为购房者提供全面的区域投资价值参考。

## 核心能力

- **数据解析能力**: 准确解析周边配套和小区信息的JSON数据结构，提取关键区域价值信息
- **配套分析能力**: 从配套数据中识别交通、教育、医疗、商业等各类设施的便利性和价值
- **价值评估能力**: 基于配套完善度、距离便利性和区域特色进行客观的价值评估
- **专业表达能力**: 将数据转换为专业、生动的区域分析文字，突出区域核心价值
- **格式规范能力**: 严格按照专业区域价值分析报告的Markdown格式要求组织内容

## 分析框架和方法

### 1. 区域价值核心要素分析
- **地理位置优势**: 基于小区地址和行政区域分析区域定位价值
- **交通枢纽价值**: 评估地铁、公交等交通配套的便利性和连通性
- **生活配套矩阵**: 分析医疗、商业、教育等配套的完善程度
- **居住品质特征**: 结合小区品质和周边环境评估居住体验

### 2. 专业价值表达模式
- **价值标签化**: 为区域特色创建专业标签（如"全球枢纽地位"、"精英圈层聚合"）
- **数据量化表达**: 将距离、数量等数据转化为价值表述
- **对比突出优势**: 通过对比分析突出区域独特价值
- **场景化描述**: 将配套优势转化为生活场景描述

## 输出结构要求

请按照以下专业结构组织区域价值分析报告：

### 1. 区域价值（核心价值概述）
- 区域核心定位和价值主张
- 关键价值要素列表（4-6个核心要点）
- 突出区域独特优势和稀缺性

### 2. 交通网络
- 轨交动脉：地铁站点及便利性分析
- 路网中枢：主要道路和高架连接
- 空港快线：机场等重要交通枢纽距离
- 公交覆盖：公交线路网络密度

### 3. 生活配套
- 医疗旗舰：重点医疗机构（分类展示）
- 商业矩阵：主要商业设施和购物中心
- 休闲图鉴：娱乐休闲和运动设施

### 4. 教育资源
- 全龄教育链：各阶段教育机构分布
- 特色优势：教育资源的独特价值点

### 5. 小区品质
- 生态美学：绿化和景观特色
- 建筑基因：建筑风格和品质特征
- 服务标准：物业服务和管理水平

## 数据处理原则

### 1. 数据准确性原则
- **严禁数据造假**：严格按照输入数据内容转换，绝对不得修改、虚构或模拟任何数据
- **仅从输入数据提取**：所有信息必须来源于提供的周边配套和小区信息数据
- **保持精确性**：距离、名称、地址信息必须准确完整
- **缺失数据处理**：对于缺失数据用适当方式表示，不得编造

### 2. 专业表达原则
- **价值导向表达**：将基础配套信息转化为价值表述
- **专业术语使用**：采用房地产行业专业表达方式
- **层次化描述**：按重要性和影响力组织内容层次
- **生动化表达**：使用生动的形容词和专业标签

### 3. 格式规范原则
- **距离表达**：统一使用公里或米为单位，添加时间描述
- **设施分类**：按类型和重要性对配套设施进行分类展示
- **重点突出**：使用markdown格式突出重要信息
- **结构清晰**：保持章节结构的逻辑性和可读性

## 专业表达模板

### 价值表达模式
- **地位表述**："作为[区域定位]的[项目特征]"
- **优势量化**："距离[设施名称] X 公里/米，[时间描述]"
- **价值标签**："[特色标签]：[具体描述]"
- **影响分析**："[配套特征]营造出[生活体验]"

### 配套描述模式
- **设施名称 + 特色标签**：如"高岛屋百货（日系顶奢）"
- **距离 + 便利性描述**：如"7分钟步行直达10号线伊犁路站"
- **功能 + 价值影响**：如"无缝连接国际国内"

## 输出格式要求

1. **使用标准Markdown格式**
2. **使用合适的标题层级** (##, ###)
3. **重要信息使用加粗** (**重要内容**)
4. **使用列表形式展示要点**
5. **保持专业性和可读性**
6. **确保内容的逻辑性和完整性**

## 禁止行为

- **严禁数据造假**：禁止修改、虚构或模拟输入数据中的任何信息
- **严禁添加外部信息**：禁止添加输入数据中不存在的任何配套设施信息
- **严禁过度简化**：禁止遗漏重要配套设施，必须充分利用所有有效数据
- 禁止使用非专业或情绪化的表达
- 禁止破坏Markdown格式规范
- 禁止忽略数据中的重要配套设施

<----------------------------(user_prompt)---------------------------->

请基于以下周边配套数据和小区信息数据，生成专业的区域价值分析markdown文档：

**小区基本信息：**

```json
${community_info}
```

**周边配套数据：**

```json
${around_data}
```

**生成要求：**

### 1. 内容要求
- **专业价值表达**：参考样例文件的表达风格，将基础配套信息转化为专业的价值表述
- **核心价值突出**：在开篇明确区域的核心价值定位和独特优势
- **配套价值化**：将距离、设施等基础信息转化为生活价值和投资价值表述
- **层次化组织**：按照区域价值→交通→配套→教育→品质的逻辑层次组织内容

### 2. 表达风格要求
- **价值导向**：突出区域的投资价值和生活价值，而非简单罗列配套
- **专业标签**：为重要配套和区域特色创建专业标签和价值标识
- **量化表述**：将距离、时间等数据转化为便利性和价值表述
- **场景化描述**：将配套优势转化为具体的生活场景和体验描述

### 3. 格式要求
- 严格按照markdown格式规范
- 使用专业的房地产行业表达方式
- 重要信息使用加粗强调
- 保持内容的逻辑性和可读性

### 4. 数据处理要求
- **严格基于输入数据**：所有内容必须基于提供的JSON数据，严禁虚构
- **充分利用数据**：必须充分利用输入数据中的所有有效配套信息
- **价值化转换**：将基础数据转换为价值表述，但不改变事实内容
- **专业化表达**：使用专业术语和表达方式提升内容品质

### 5. 质量标准要求
- **内容深度**：每个章节都要有充实的内容，不能只是简单罗列
- **价值挖掘**：深度挖掘每个配套设施的价值意义和生活影响
- **专业水准**：达到专业房地产分析报告的质量标准
- **客户导向**：站在购房者角度分析区域价值和投资潜力

## 样例文件风格参考

请参考以下表达风格：

### 价值表述风格
- "作为上海闵行区金虹桥板块的顶级住宅标杆"
- "坐拥虹桥高尔夫球场南向景观"
- "62% 超高绿化率营造出都市绿洲般的居住环境"
- "完美融合国际化社区氛围与私密尊贵体验"

### 配套描述风格
- "全球枢纽地位：距虹桥机场 6.9 公里，无缝连接国际国内"
- "精英圈层聚合：企业高管与外籍人士占比超 70%"
- "资产硬通货属性：196,262 元 /㎡均价彰显顶级保值力"

### 设施标签风格
- "高岛屋百货（日系顶奢）"
- "尚嘉中心（LVMH 旗舰综合体）"
- "和睦家国际医院（高端私立）"
- "上海第六人民医院（三甲公立）"

请严格按照以上要求和风格，生成高质量的区域价值分析文档。


<----------------------------(community_info)---------------------------->
{
"status": "SUCC_DONE",
"message": "成功",
"data": {
"busiName": "凉城",
"townName": "大宁路街道",
"address": "平型关路1083弄",
"districtName": "静安区",
"cityCode": "310100",
"latitude": 31.281544,
"typeName": "商务住宅;住宅区;住宅小区",
"beikeName": "慧芝湖花园（三期）",
"cityId": 310100,
"beikeId": 5020069179760703,
"layoutsSummaryMap": {
"1室2厅": {
"roomCount": 1,
"balconyCount": 0,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/d697791a-32be-461f-97bf-807266a1b262.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/a33c37f1-25c1-4e45-bdce-790ca28dd702.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/22630147-9fef-4447-87a8-411177125b75.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/55743f0d-c688-4400-8797-32363b386391.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/5f9ce615-2fd3-4d01-b95a-1be41ddcb495.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/09513af2-c135-45ea-b8d4-144c78d94c9e.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d2276afc-e980-468c-bdd8-e3af192b18dd.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/5eb5260e-c2a5-4808-b7d0-13695a0e774e.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/cf69a04e-9234-4675-8434-87f8dde4fa5f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/e2dfc091-472f-464c-84be-3075377c4a33.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/37779854-64b7-42e6-bcd7-458656857ecf.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/ab966172-90cb-4116-b5f5-7af48e78fdc1.jpg"
],
"areaMin": 70.29,
"areaMax": 88.56,
"setNum": 12,
"toiletCount": 1,
"name": "1室2厅",
"hallCount": 2,
"kitchenCount": 1
},
"1室1厅": {
"roomCount": 1,
"balconyCount": 1,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/90f2a195-1269-48a8-bdb7-3276dae69864.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/c5abe500-df5f-43ae-9810-9a99cbf3931c.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/2bedf1cb-7343-4a68-bb71-4f66c0a5fb72.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/16021c5f-9c3b-4487-a118-be073e4fc6ec.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/87e03e8a-9a87-41f4-b848-82626865b3ba.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/994500f1-63b5-495c-9cdd-acca6fb8eff4.jpg"
],
"areaMin": 72.44,
"areaMax": 79.00,
"setNum": 6,
"toiletCount": 1,
"name": "1室1厅",
"hallCount": 1,
"kitchenCount": 1
},
"2室1厅": {
"roomCount": 2,
"balconyCount": 1,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/31900555-ea7f-4632-a826-be9c3b950e60.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/704b445c-54a3-4bdf-8a89-e26d968f2d6b.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/54a82bf9-c9b8-47c7-aa78-4f894631dac9.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/54bc33a0-21f4-44fc-b8b3-7ccd77d9220f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/a4e35d74-ea48-4c10-a61e-ddb897fac06c.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/93d825f5-b603-4f2d-9bb2-ad84a6a9b5c8.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/1688059e-43f9-4b36-bb4d-97ccb459b0f9.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/6188fcaa-165f-4252-af0d-2337b5e676cb.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/ba8df067-ecab-4879-95be-1454375f57c9.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/4aa5dc32-b24d-4307-8fe5-e59c5f251d05.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/baec009d-03fa-47df-8b06-f3c00871c0c9.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d022237d-525d-4bfe-8e8e-a989c5edb8f8.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d33c326b-dbbb-45b4-852c-43c97c6b26c0.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/c8845078-8090-47ee-8c6d-28c748e289c3.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/c17813b5-4d95-4887-a0fe-53bf0169e52b.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/b6f77457-ba09-443a-97a0-7a4dc63292e0.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/86227575-e2a6-4a7b-b2b0-5edb6f09aac1.jpg"
],
"areaMin": 73.04,
"areaMax": 107.00,
"setNum": 34,
"toiletCount": 2,
"name": "2室1厅",
"hallCount": 1,
"kitchenCount": 0
},
"2室2厅": {
"roomCount": 2,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/b37361d4-e2e2-45c0-abb7-53aaec0cea64.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/29a1b504-85f4-4816-8d96-e1c45e4ef62f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/a128c2d5-af73-42af-a4ed-962ea5a575dd.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/17a654c7-516a-45f7-ba77-b7f6d7635a59.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/68d77614-ca26-4246-b02b-afd4d3856ba6.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/e428e6d2-0f55-46dd-b11a-f5d2992a8b12.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/5bb9938d-20d6-42dd-8626-535f98142a92.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d4cfc4ae-8577-4ded-b89e-d4395c19bdff.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/5c25520e-a9ba-4fea-ba50-f114b0045eec.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/4c184efa-17c4-41c5-95b1-f850a5c84b24.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/224c4898-d821-4a39-a0b6-ec37f974bf57.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/7ea5d175-4464-49a2-95d8-d81c71490b4f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/b90b8f74-2fcc-472c-95a1-cf1a3b2d3fd4.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/f6343115-21bc-4860-a9fb-2a56bb8e9b8b.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/9f4b5bf1-2897-4fc2-a0c0-1671664132ad.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/f8a34698-460e-4081-be76-ac4f6358783f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/c007286a-ee45-49b8-bec5-c55b067ff2f1.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/de847ef6-1f9e-45d9-a926-2993d69c231b.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d46e126c-d2fc-4c77-98d7-78011227e0a8.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/1a83bf40-7ebb-4175-905f-e05ab13cc08d.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/5de50209-8735-4378-8a2f-1fdd4d1664af.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/116bc488-be3c-4869-bde9-6a3d92a818dc.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/cdc71dbf-85f2-436f-8d24-aa2039c46c91.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/7e85149e-ae3e-43fa-ad7e-e66891b96feb.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/fec5dc11-fbfb-48ae-bc7c-84c2739a5a4d.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/9686fde7-ec68-4967-b3ed-13035342c4db.jpg"
],
"areaMin": 72.44,
"areaMax": 107.69,
"setNum": 52,
"name": "2室2厅",
"hallCount": 2
},
"3室1厅": {
"roomCount": 3,
"balconyCount": 2,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/575d21f1-a006-4a94-a7c6-580e44526012.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/78ca22c9-c7e0-458a-ad76-a26709cc5279.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/9ea813cb-cb29-489e-b706-9ea009769041.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/0cb94bb2-f993-42cc-835b-eaaf34a80b98.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/f4710f84-e12b-4f0e-a7dc-47964d08a5c2.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/d4dccfb1-4a01-4d40-b049-078cd495872e.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/a50a3d97-1d9a-4d75-b723-66b868508064.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/0399cf1d-a125-48c7-9786-09cc0b001644.jpg"
],
"areaMin": 80.98,
"areaMax": 154.10,
"setNum": 24,
"toiletCount": 2,
"name": "3室1厅",
"hallCount": 1,
"kitchenCount": 1
},
"3室2厅": {
"roomCount": 3,
"balconyCount": 2,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/9be33f94-6fc7-4e25-938d-f48ceabede2f.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/274d65d5-c83c-4c18-940b-3aa8274a889a.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/75b1a0bc-0002-42e2-8f74-4444388048e2.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/8fa57921-6ae9-40ec-b908-1354ec8c96a1.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/35bd1042-5590-40e4-be80-121200c606fd.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/08041c30-6243-4a2a-a074-edc7a491bd83.jpg"
],
"areaMin": 88.35,
"areaMax": 109.77,
"setNum": 18,
"toiletCount": 1,
"name": "3室2厅",
"hallCount": 2,
"kitchenCount": 1
},
"4室1厅": {
"roomCount": 4,
"balconyCount": 1,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/63882253-ae12-4104-bcbc-9ec2b2568366.jpg"
],
"areaMin": 193.24,
"areaMax": 193.24,
"setNum": 4,
"toiletCount": 3,
"name": "4室1厅",
"hallCount": 1,
"kitchenCount": 1
},
"4室2厅": {
"roomCount": 4,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/fddab51e-fdb1-42f8-9707-3bfab6830d8e.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/4ac8187e-a9fc-480a-998c-1f5fee6af129.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/69713ad8-471c-446b-82f0-d23a70dfb323.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/c64aee10-36b7-441f-8fe9-9b658f6b411a.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/b07ed0cd-fc36-40df-ad78-143ccc8684a9.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/106eef48-0888-4f07-b308-895e92ad2e86.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/ef87c00b-dbb8-422f-ad49-dca8bc7b43eb.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/6c658490-a3c6-4998-9384-d5bb0e8f5192.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/4dd843f6-7dfe-4d75-8fd1-9c87461fe2dd.jpg",
"https://oss-consumer.ebaas.com/community_layout/129712/24d32dab-003c-4c45-873b-82c1753ff122.jpg"
],
"areaMin": 95.16,
"areaMax": 173.82,
"setNum": 40,
"name": "4室2厅",
"hallCount": 2
},
"4室3厅": {
"roomCount": 4,
"balconyCount": 1,
"picUrl": [
"https://oss-consumer.ebaas.com/community_layout/129712/8e3ff767-b10e-441b-acd5-3cbd71781875.jpg"
],
"areaMin": 184.21,
"areaMax": 184.21,
"setNum": 4,
"toiletCount": 3,
"name": "4室3厅",
"hallCount": 3,
"kitchenCount": 1
}
},
"districtId": 310106,
"cityName": "上海市",
"formattedAddress": "上海市静安区大宁路街道慧芝湖花园",
"picturesMap": {
"景观带": [
"https://oss-consumer.ebaas.com/community_picture/129712/9a6a9e4c-910a-4c59-a23d-c7139c65d977.jpg"
],
"远景": [
"https://oss-consumer.ebaas.com/community_picture/129712/669a8c30-dd3e-47ec-8d84-8eb1270af886.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/6e8cdf8e-0f8d-4ff3-ac90-5e2b43cd6993.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/7568bea0-8562-46e7-918c-4030112f554b.jpg"
],
"楼栋": [
"https://oss-consumer.ebaas.com/community_picture/129712/61b50115-7f66-407e-84cc-4fe411328ba4.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/41998c0d-0c8b-4166-b9ac-b88e5fabbec1.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/8b1ebf63-1bd2-43cd-bef4-19be36028123.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/84d3ed2d-cd25-4234-b216-712b8a55d9e9.jpg"
],
"出入口": [
"https://oss-consumer.ebaas.com/community_picture/129712/ffa6345b-9491-44d5-b89f-85030a77db01.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/4e5ccd03-e53a-4947-8fa5-92377fb667de.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/bf36c1dc-1d96-4b92-8e0a-9557faa8256a.jpg"
],
"入户门": [
"https://oss-consumer.ebaas.com/community_picture/129712/5002fc3d-6ac5-4159-a7ee-6462ab03ae0b.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/21dcdab4-096a-4017-bdda-e70909762b09.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/2b86547c-0a1e-4966-8420-b282d98c6c23.jpg"
],
"分布图    ": [
"https://oss-consumer.ebaas.com/community_picture/129712/83c455ea-11dc-47d1-8202-5221a4406aa1.jpg"
],
"其他": [
"https://oss-consumer.ebaas.com/community_picture/129712/9374425c-dde1-4af3-8dcb-7fbaa8036bed.jpg"
],
"道路": [
"https://oss-consumer.ebaas.com/community_picture/129712/737a59b1-2db1-4297-8f58-e9573233aeee.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/ff213483-ee1e-4e92-9c24-2c12820d4bb6.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/1fe2ba85-8cf6-4db8-8516-609d0c0014ff.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/e8a6de00-afa1-4ee0-b58c-a2e42f7f0b09.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/e2644c9f-e70a-4cd0-a07b-cba60cc878b6.jpg"
],
"停车场": [
"https://oss-consumer.ebaas.com/community_picture/129712/6ffd978c-36a0-47db-895a-dd56938174f7.jpg",
"https://oss-consumer.ebaas.com/community_picture/129712/fcd4718b-9600-4ecd-9b7c-298e472fe9d7.jpg"
]
},
"name": "慧芝湖花园",
"location": "121.458773,31.281544",
"id": 1,
"provinceName": "上海市",
"detail": {
"buildNum": 9,
"blockName": "大宁",
"powerdDesc": "民电",
"setParkingFee": "300",
"parkingNum": 3494,
"gasDesc": "3元/m³",
"parkingRate": "1.00:0.70",
"propertyType": "住宅",
"id": 129712,
"propertyFee": "2.7元/月/㎡",
"upParkingNum": 0,
"propertyYears": "50/70",
"heatingDesc": "自采暖",
"downParkingNum": 0,
"updateTime": "2025-01-09 15:11:45",
"version": 2,
"volumeRate": 2.50,
"developerCorp": "嘉华(中国)投资有限公司",
"greenRate": 0.45,
"createTime": "2024-12-31 15:00:49",
"propertyName": "龙湖物业",
"blockCd": "310106106",
"buildMaxYear": "2009",
"commBelong": "商品房/使用权",
"waterDesc": "民水",
"propertyPhone": "021-66525123",
"houseNum": 3526,
"buildMinYear": "2004",
"buildingType": "板楼"
},
"longitude": 121.458773
},
"error": null
}

<----------------------------(around_data)---------------------------->
{
"count": "450",
"info": "OK",
"infocode": "10000",
"pois": [
{
"address": "平型关路1063号-2",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.4"
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "56",
"id": "B0FFGHKE9V",
"importance": [],
"location": "121.458218,31.281372",
"name": "百果园(慧芝湖花园店)",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/0f0c80b1cdb58c164714ea8bd4706b6b"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/db4c34e9dc606b7a1a30f4bfe16ff848"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/da9db36646b91f7989c3a57b5a71e06f"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "021-56426132",
"type": "购物服务;综合市场;果品市场",
"typecode": "060704"
},
{
"address": "平型关路992-996号",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "166",
"id": "B0FFJACO2B",
"importance": [],
"location": "121.457970,31.280217",
"name": "益丰大药房(平型关路店)",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/c4b5d5ee5ff78b6c9c20df2feb6ad3ee"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": [],
"type": "医疗保健服务;医药保健销售店;药房",
"typecode": "090601"
},
{
"address": "广中路765号771号嘉悦会1层",
"adname": "静安区",
"biz_ext": {
"cost": [],
"meal_ordering": "0",
"open_time": "07:00-22:00",
"opentime2": "07:00-22:00",
"rating": "4.7"
},
"biz_type": "diner",
"childtype": "320",
"cityname": "上海市",
"distance": "199",
"id": "B0FFM45HIF",
"importance": [],
"location": "121.457972,31.279887",
"name": "星巴克(上海慧芝湖店)",
"parent": "B00157HWK9",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/ec3440dbf3d8f9de23da5c7a0cf58ad7"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/95ae8b820c5da7a43d84a54e82b099e1"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0FFM45HIF/comment/295fd814e58a83ea08948f05a8ea7a4d_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": "021-60782939;021-66270020;4008206998",
"type": "餐饮服务;咖啡厅;星巴克咖啡",
"typecode": "050501"
},
{
"address": "北宝兴路900号(北宝兴路灵石路)",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "254",
"id": "B00155G7IZ",
"importance": [],
"location": "121.460633,31.283192",
"name": "上海市大宁国际小学",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/42e97b3c189b949ff531f968fc36907d"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/02aff77941b47d489145056d1508ffd6"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "021-66520303",
"type": "科教文化服务;学校;小学",
"typecode": "141203"
},
{
"address": "107路;547路;767路;79路;858路;862路;912路;944路",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "305",
"id": "BV10025188",
"importance": [],
"location": "121.456897,31.279319",
"name": "广中路平型关路(公交站)",
"parent": [],
"photos": [],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "2",
"tel": [],
"type": "交通设施服务;公交车站;公交车站相关",
"typecode": "150700"
},
{
"address": "灵石路217号5楼1500室,6楼1600-1613室",
"adname": "静安区",
"biz_ext": {
"cost": "127.00",
"meal_ordering": "0",
"open_time": "11:00-14:00 17:00-21:00",
"opentime2": "11:00-14:00；17:00-21:00",
"rating": "4.6"
},
"biz_type": "diner",
"childtype": "320",
"cityname": "上海市",
"distance": "308",
"id": "B0FFGDLJ3K",
"importance": [],
"location": "121.456549,31.283560",
"name": "和记小菜(大宁店)",
"parent": "B0IKV5HDJV",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/511666e7f72267e245ab319cfc6abca0"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/63290b1c1d58c68d152099d2a2eda095"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/6b07de206e28b7ddf8bcedca17712c77"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "15800478409",
"type": "餐饮服务;中餐厅;中餐厅",
"typecode": "050100"
},
{
"address": "广中路865号(近马戏城地铁站)",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "348",
"id": "B00156NTH9",
"importance": [],
"location": "121.455707,31.279827",
"name": "登特口腔",
"parent": [],
"photos": [
{
"title": [],
"url": "https://aos-comment.amap.com/B00156NTH9/comment/835abbe1349a47cdf4a4d4e69aa4e4f3_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B00156NTH9/comment/8d9edc21dffef4328c9571949c75abcd_2048_2048_80.jpg"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/9a92ef1454da4616487f1e354dd76943"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "2",
"tel": "021-56350183;021-56350520",
"type": "医疗保健服务;专科医院;口腔医院",
"typecode": "090202"
},
{
"address": "灵石路236号",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "355",
"id": "B00156NTHD",
"importance": [],
"location": "121.455405,31.282937",
"name": "大宁国际第二幼儿园(灵石路)",
"parent": [],
"photos": [
{
"title": [],
"url": "https://aos-comment.amap.com/B00156NTHD/comment/b420cb975b439f172603a3c403b0848b_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B00156NTHD/comment/ae4225933fb11bd16e24f904eabf4cea_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B00156NTHD/comment/2d1e89d173ed4eefd9a6b87dc4857d52_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "021-53931500",
"type": "科教文化服务;学校;幼儿园",
"typecode": "141204"
},
{
"address": "广中路657号8幢",
"adname": "虹口区",
"biz_ext": {
"cost": "94.00",
"meal_ordering": "0",
"open_time": "10:00-22:00",
"opentime2": "【正常】全年10:00-22:00",
"rating": "4.5"
},
"biz_type": "diner",
"childtype": [],
"cityname": "上海市",
"distance": "376",
"id": "B0FFK8AQY7",
"importance": [],
"location": "121.462425,31.280244",
"name": "有家川菜(广中店)",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/3e655832f626597a566b1a8646de0f13"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/756c7e32cebbe47d8a2f467f4cb0a12b"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/686029845d4f21e7461e0d5596ea96e3"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "13691333783;16602161298",
"type": "餐饮服务;餐饮相关场所;餐饮相关",
"typecode": "050000"
},
{
"address": "广中路909号(上海马戏城地铁站1号口步行220米)",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.5"
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "433",
"id": "B0JACLVBU1",
"importance": [],
"location": "121.454650,31.279880",
"name": "自然运动·普拉提(宝华店)",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/015f02ddfe5fcf032b46d9a20e790107"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/e665c12777211c760b2a4873a3123401"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": "15021099828;15821160914",
"type": "购物服务;体育用品店;体育用品店",
"typecode": "060900"
},
{
"address": "共和新路2395弄3-5号",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.7"
},
"biz_type": [],
"childtype": "320",
"cityname": "上海市",
"distance": "489",
"id": "B0FFLEVH1V",
"importance": [],
"location": "121.453994,31.279904",
"name": "宝华现代城商业街(宝华现代城1期店)",
"parent": "B0FFH1CPLA",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/42b6eeb7f855819469f745b4f98a2df6"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "021-66308108",
"type": "购物服务;特色商业街;特色商业街",
"typecode": "061000"
},
{
"address": "北宝兴路624号17栋F201-202室",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": "202",
"cityname": "上海市",
"distance": "500",
"id": "B0JB4HZSHP",
"importance": [],
"location": "121.461121,31.277523",
"name": "上海赞瞳眼科诊所",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/1e579fba8a44c7ebbc0529ab1c0f7fa2"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/aafbcb46fca9cbeef596047d9ded8552"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": [],
"type": "医疗保健服务;医疗保健服务场所;医疗保健服务场所",
"typecode": "090000"
},
{
"address": "北宝兴路624号百联莘荟购物中心F1层",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.6"
},
"biz_type": [],
"childtype": "202",
"cityname": "上海市",
"distance": "500",
"id": "B0JKZHNEJC",
"importance": [],
"location": "121.460966,31.277459",
"name": "盒马奥莱(百联莘荟购物中心店)",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/fd4883feb53d40a5b71ee093dddb93d7_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/caff43eb0910cf20ec94f8b7501f1b7a_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0JKZHNEJC/comment/0e574a3df537a5ecbf05bd2b3e0a2235_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": [],
"type": "购物服务;购物相关场所;购物相关场所",
"typecode": "060000"
},
{
"address": "北宝兴路624号",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.5"
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "500",
"id": "B0FFMGE5SZ",
"importance": [],
"location": "121.460966,31.277459",
"name": "百联莘荟购物中心",
"parent": [],
"photos": [
{
"title": [],
"url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c1bf91c4adc30b3db5fda16d9fd152b7_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c320b3ada7f185c3ae5558a4c23acbfb_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0FFMGE5SZ/comment/c9d5381a9fbe416154763a9cbb417fde_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": [],
"type": "购物服务;商场;购物中心|购物服务;商场;普通商场",
"typecode": "060101|060102"
},
{
"address": "北宝兴路624号",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": "4.7"
},
"biz_type": [],
"childtype": "202",
"cityname": "上海市",
"distance": "505",
"id": "B0KGK5YS57",
"importance": [],
"location": "121.461010,31.277428",
"name": "盒马NB(静安百联莘荟店)",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "https://aos-comment.amap.com/B0KGK5YS57/comment/3b80846cceedb9d47474f6144030792d_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0KGK5YS57/comment/a8a60569e6bda30f5d10da6ee6381e6d_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0KGK5YS57/comment/f55fbd54ea9c62c78accdf7c829c4a11_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": [],
"type": "购物服务;服装鞋帽皮具店;品牌服装店",
"typecode": "061101"
},
{
"address": "大宁路181弄15号(上海马戏城地铁站2号口步行380米)",
"adname": "静安区",
"biz_ext": {
"cost": [],
"rating": []
},
"biz_type": [],
"childtype": [],
"cityname": "上海市",
"distance": "518",
"id": "B00156OCZV",
"importance": [],
"location": "121.455184,31.278038",
"name": "上海市静安区大宁路小学",
"parent": [],
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/8b66a1b7f0a8b5ccb51a9acad8132cc6"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "021-56383173",
"type": "科教文化服务;学校;小学",
"typecode": "141203"
},
{
"address": "北宝兴路624号百联莘荟购物中心F1层",
"adname": "静安区",
"biz_ext": {
"cost": "28.00",
"meal_ordering": "0",
"open_time": "09:00-22:30",
"opentime2": "【正常】全年09:00-22:30",
"rating": "4.6"
},
"biz_type": "diner",
"childtype": "202",
"cityname": "上海市",
"distance": "532",
"id": "B0H3LDJL0J",
"importance": [],
"location": "121.461607,31.277424",
"name": "德克士(百联莘荟购物中心店)",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/536aa53d85646ee609fac9d17cc0082d"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/dfcb84669e20877c8d4fe909bfe62ab3"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/90f7706a8c003224549f3756e5d3a263"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "0",
"tel": "15000773727;17269519800;17521702825",
"type": "餐饮服务;快餐厅;快餐厅",
"typecode": "050300"
},
{
"address": "北宝兴路624号F101+F102室",
"adname": "静安区",
"biz_ext": {
"cost": "28.00",
"meal_ordering": "0",
"open_time": "07:30-18:00 07:30-22:00",
"opentime2": "【特殊】2022-01-31至2022-01-3107:30-18:00；【正常】全年07:30-22:00",
"rating": "4.5"
},
"biz_type": "diner",
"childtype": "202",
"cityname": "上海市",
"distance": "532",
"id": "B0HA9N7DGB",
"importance": [],
"location": "121.461579,31.277400",
"name": "Tims天好咖啡(百联莘荟购物中心店)",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/4ea8efe554bcc09a280ed7b75ca97731"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/767b9cadc406ec79b618c89c1dffdcee"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/238545db89dbd650bc08e11306e86901"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": "021-51556131",
"type": "餐饮服务;咖啡厅;咖啡厅",
"typecode": "050500"
},
{
"address": "北宝兴路624号院内F独栋",
"adname": "静安区",
"biz_ext": {
"cost": [],
"meal_ordering": "0",
"open_time": "11:00-24:00",
"opentime2": "11:00-24:00",
"rating": "4.7"
},
"biz_type": "diner",
"childtype": "202",
"cityname": "上海市",
"distance": "542",
"id": "B0I215OZEX",
"importance": [],
"location": "121.460847,31.277002",
"name": "颐和国际公馆.江浙精致私房菜(百联莘荟购物中心店)",
"parent": "B0FFMGE5SZ",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/894acb9f8ba29d52c8099a0164f55ea0"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0I215OZEX/comment/213c23ec727a85426072ebcf5be110da_2048_2048_80.jpg"
},
{
"title": [],
"url": "https://aos-comment.amap.com/B0I215OZEX/comment/c585d53850b8395a1949ecabd212fdc5_2048_2048_80.jpg"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": "19370792117",
"type": "餐饮服务;餐饮相关场所;餐饮相关",
"typecode": "050000"
},
{
"address": "共和新路2449号泛欧现代大厦1413室",
"adname": "静安区",
"biz_ext": {
"cost": "3568.00",
"rating": "4.8"
},
"biz_type": [],
"childtype": "202",
"cityname": "上海市",
"distance": "587",
"id": "B00156ZA8K",
"importance": [],
"location": "121.452704,31.280586",
"name": "宫品海参(静安大宁专卖店)",
"parent": "B00155FHSP",
"photos": [
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/f9cbf1f4e501916d242c38f5fdd87624"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/784fb569005d8a738575fc03952cb953"
},
{
"title": [],
"url": "http://store.is.autonavi.com/showpic/33501e3683d5921147122a20d212fa96"
}
],
"pname": "上海市",
"poiweight": [],
"shopid": [],
"shopinfo": "1",
"tel": "021-61395665;15000731240",
"type": "购物服务;综合市场;水产海鲜市场",
"typecode": "060706"
}
],
"status": "1",
"suggestion": {
"cities": [],
"keywords": []
}
}
