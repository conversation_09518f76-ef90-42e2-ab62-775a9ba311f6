package com.dcai.aixg.domain.aiagent.functioncall;

import com.dcai.aixg.domain.aiagent.generate.GenerateService;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.pro.LaunchGeneratePO;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.consumer.api.community.CommunityAPI;
import com.ejuetc.consumer.api.delegation.ApiQueryListPO;
import com.ejuetc.consumer.api.delegation.DelegationAPI;
import com.ejuetc.consumer.web.vo.DelegationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 生成API实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class FunctionCallService {

    @Autowired
    @Qualifier("delegationAPI_SaaSAPIFeign")
    private DelegationAPI delegationAPI;

    @Autowired
    @Qualifier("communityAPI_SaaSAPIFeign")
    private CommunityAPI communityAPI;

    @Autowired
    private GenerateService generateService;



    @Tool(description = "查询房源委托信息")
    public ApiResponse<List<DelegationVO>> queryDelegation(@ToolParam(description = "房源委托查询参数") DelegationQueryPO po) {
        ApiResponse<List<DelegationVO>> query = delegationAPI.query(new ApiQueryListPO().setDelegationIds(po.getIds()));
        return query;
    }

//    @Tool(description = "查询小区信息")
//    public ApiResponse<String> generate(@ToolParam(description = "小区查询参数") CommunityQueryPO po) {
//        return communityAPI.bind(po.getAddress(), po.getName());
//    }

    @Tool(description = "发起生成任务")
    public ApiResponse<GenerateDTO> runGenerate(@ToolParam(description = "生成任务代码") String configCode, @ToolParam(description = "生成任务参数") Map<String, Object> request) {
        return generateService.launch(new LaunchGeneratePO(configCode, request, LaunchGeneratePO.RunMode.SYNC));
    }



}
