package com.dcai.aixg.domain.aiagent.flow;

import static com.dcai.aixg.dto.FlowDTO.Status.PROCESSING;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.api.BrokerAPI;
import com.dcai.aixg.api.WriteAPI;
import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.generate.Generate;
import com.dcai.aixg.domain.aiagent.generate.GenerateRpt;
import com.dcai.aixg.dto.FlowDTO;
import com.dcai.aixg.dto.ProcessDTO;
import com.dcai.aixg.pro.LaunchFlowPO;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.MapContent;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderColumn;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作流实例实体
 * <p>
 * 表示一个具体的工作流执行实例，按顺序执行多个Generate任务
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("工作流实例")
@Table(name = "tb_ai_flow")
@Where(clause = "logic_delete = 0")
public class Flow extends ProcessBase<Flow> {

    @Id
    @Comment("工作流ID")
    @GeneratedValue(generator = "flow_id")
    @SequenceGenerator(name = "flow_id", sequenceName = "seq_flow_id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "config_id", columnDefinition = "bigint(20) COMMENT '工作流配置ID'", nullable = false)
    private FlowConfig config;

    @Type(JsonUT.class)
    @Column(name = "request", columnDefinition = "text COMMENT '请求内容'", nullable = false)
    private JSONObject request;

    @Enumerated(EnumType.STRING)
    @Comment("流程状态")
    @Column(name = "status", nullable = false)
    private FlowDTO.Status status;

//    @Column(name = "total_steps", columnDefinition = "int COMMENT '总步骤数'")
//    private Integer totalSteps;
//
//    @Column(name = "current_step", columnDefinition = "int COMMENT '当前步骤'")
//    private Integer currentStep = 0;

    @OrderColumn(name = "sort", columnDefinition = "int COMMENT '步骤顺序'")
    @OneToMany(mappedBy = "flow", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Generate> generates = new ArrayList<>();

//    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
//    @JoinColumn(name = "current_generate_id", columnDefinition = "bigint(20) COMMENT '最后一个生成任务的ID'")
//    private Generate currentGenerate;

    @Comment("当前处理器类型")
    @Enumerated(EnumType.STRING)
    @Column(name = "current_process_type")
    private ProcessDTO.Type currentProcessType;

    @Column(name = "current_process_id", columnDefinition = "bigint(20) COMMENT '当前处理器ID'")
    private Long currentProcessId;

    @Comment("通知状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "src_type")
    private FlowDTO.SrcType srcType;

    @Column(name = "src_id", columnDefinition = "bigint(20) COMMENT '发起流程来源ID'")
    private Long srcId;

    @Comment("通知状态")
    @Enumerated(EnumType.STRING)
    @Column(name = "notify_status")
    private ResponseStatus notifyStatus;

    @Column(name = "notify_message", columnDefinition = "text COMMENT '通知消息'")
    private String notifyMessage;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "current_node_id", columnDefinition = "bigint(20) COMMENT '流程根节点ID'")
    private FlowNode currentNode;

    @Transient
    private ProcessBase<?> currentProcess;

    /**
     * 基于PO构造函数
     */
    public Flow(FlowConfig config, LaunchFlowPO po) {
        this.srcType = po.getSrcType();
        this.srcId = po.getSrcId();
        this.config = config;
        this.properties = config.getProperties();
        this.request = new JSONObject(po.getRequest());
        this.status = FlowDTO.Status.WAIT;
    }

    public ProcessBase<?> createNextProcess() {
        if (isCompleted()) return null;

        //因异步截断的流程,当前节点若执行未成功,则直接返回用于终止流程
        if (getCurrentProcess() != null && !getCurrentProcess().isSuccess()) {
            makeCompleted(getCurrentProcess());
            return getCurrentProcess();
        }

        if (currentNode == null) {
            timerStart();
            currentNode = config.getRootNode();
            status = PROCESSING;
        } else {
            currentNode = currentNode.nextNode(this);
        }
        if (currentNode != null) {
            ProcessBase<?> newProcess = currentNode.newProcess(this);
            newProcess.save();
            setCurrentProcess(newProcess);
            return newProcess;
        } else {
            makeCompleted(getCurrentProcess());
            return null;
        }
    }

    private void setCurrentProcess(ProcessBase<?> processBase) {
        currentProcessType = processBase.getProcessType();
        currentProcessId = processBase.getId();
        currentProcess = processBase;
        if (processBase instanceof Generate generate)
            generates.add(generate);
    }

    private ProcessBase<?> getCurrentProcess() {
        if (currentProcess == null && currentProcessType != null && currentProcessId != null)
            currentProcess = switch (currentProcessType) {
                case FLOW -> getBean(FlowRpt.class).getReferenceById(currentProcessId);
                case GENERATE -> getBean(GenerateRpt.class).getReferenceById(currentProcessId);
                default -> throw new CodingException("意料之外的处理器类型[%s]", currentProcessType);
            };
        return currentProcess;
    }

    public void makeCompleted(ProcessBase<?> process) {
        if (!process.equals(currentProcessType, currentProcessId)) throw new IllegalArgumentException("最后一个生成任务不匹配");
        if (isCompleted()) return;
        timerStop();
        status = process.isSuccess() ? FlowDTO.Status.SUCCESS : FlowDTO.Status.FAILED;
        setErrorMessage(process.getErrorMessage());
        setResponse(process.getResponse());
    }

    public boolean isCompleted() {
        return status == FlowDTO.Status.SUCCESS || status == FlowDTO.Status.FAILED;
    }

    @Override
    public ProcessDTO.Type getProcessType() {
        return ProcessDTO.Type.FLOW;
    }

    public boolean isSuccess() {
        return status == FlowDTO.Status.SUCCESS;
    }

    public boolean isFailed() {
        return status == FlowDTO.Status.FAILED;
    }

    public String getPrevGenerateResponse(Generate generate) {
        int currentIndex = generates.indexOf(generate);
        if (currentIndex <= 0) {
            return null; // 第一个Generate或者找不到当前Generate
        }
        Generate prevGenerate = generates.get(currentIndex - 1);
        return prevGenerate != null ? prevGenerate.getResponse() : null;
    }

    public void notifySource() {
        try {
            ApiResponse<?> notifyResp = switch (srcType) {
                case TASK -> getBean(WriteAPI.class).notifyWriteResult(srcId, status, getResponse());
                case TWEET -> getBean(BrokerAPI.class).flowCallBack(srcId, status, getResponse());
                case SUBFLOW -> {
                    getBean(FlowService.class).execFlow(srcId);
                    yield succ();
                }
            };
            notifyStatus = notifyResp.getStatus();
            notifyMessage = notifyResp.getMessage();
        } catch (Exception e) {
            notifyStatus = ResponseStatus.FAIL_BIZ;
            notifyMessage = e.getMessage();
        }
    }

    public boolean isWait() {
        return status == FlowDTO.Status.WAIT;
    }

    public Flow getParentFlow() {
        if (!hasParentFlow())
            throw new CodingException("流程[%s]的来源类型为[%s],没有父流程", getId(), getSrcType());
        return getBean(FlowRpt.class).getReferenceById(srcId);
    }

    public boolean hasParentFlow() {
        return srcType == FlowDTO.SrcType.SUBFLOW;
    }
}
