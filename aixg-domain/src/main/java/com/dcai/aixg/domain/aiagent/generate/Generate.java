package com.dcai.aixg.domain.aiagent.generate;

import static com.dcai.aixg.domain.aiagent.functioncall.FunctionCall.getRunningFunctionCall;
import static com.dcai.aixg.dto.GenerateDTO.Status.FAILED;
import static com.dcai.aixg.dto.GenerateDTO.Status.GENERATING;
import static com.dcai.aixg.dto.GenerateDTO.Status.SUCCESS;
import static com.dcai.aixg.dto.GenerateDTO.Status.WAIT;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

import com.alibaba.fastjson.JSONObject;
import com.dcai.aixg.domain.aiagent.commons.ProcessBase;
import com.dcai.aixg.domain.aiagent.flow.Flow;
import com.dcai.aixg.domain.aiagent.functioncall.FunctionCall;
import com.dcai.aixg.domain.commons.ThreadLocalStack;
import com.dcai.aixg.dto.GenerateDTO;
import com.dcai.aixg.dto.ProcessDTO;
import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.usertype.JsonUT;
import com.ejuetc.commons.base.usertype.MapContentUT;
import com.ejuetc.commons.base.valueobj.Command;
import com.ejuetc.commons.base.valueobj.MapContent;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@Entity
@NoArgsConstructor
@Comment("生成任务")
@Table(name = "tb_ai_generate")
@Where(clause = "logic_delete = 0")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", columnDefinition = "enum('LLM_GEN','POLICY_COMMENT','SALE_COMPARE','LIFE_EXPERT','HOUSING_EXPERT','MONTHLY_REPORT','INDUSTRY_ANALYST','VALUE_EVALUATION') COMMENT '类型'")
public abstract class Generate extends ProcessBase<Generate> implements Command {

    @Id
    @Comment("任务ID")
    @GeneratedValue(generator = "generate_id")
    @SequenceGenerator(name = "generate_id", sequenceName = "seq_generate_id")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "flow_id", columnDefinition = "bigint(20) COMMENT '父工作流ID'")
    private Flow flow;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false, insertable = false, updatable = false)
    private GenerateDTO.Type type;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "config_id", columnDefinition = "bigint(20) COMMENT '配置ID'")
    private GenerateConfig config;

    @Type(JsonUT.class)
    @Column(name = "request", columnDefinition = "longtext COMMENT '请求内容'")
    private JSONObject request;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "prepare_result", columnDefinition = "longtext COMMENT '预处理结果'")
    private String prepareResult;

    @Enumerated(EnumType.STRING)
    @Comment("状态")
    @Column(name = "status", nullable = false)
    private GenerateDTO.Status status = WAIT;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "parent_generate_id", columnDefinition = "bigint(20) COMMENT '父生成任务'")
    private Generate parentGenerate;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_1", columnDefinition = "longtext COMMENT '处理过程信息1'")
    private String processInfo1;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_2", columnDefinition = "longtext COMMENT '处理过程信息2'")
    private String processInfo2;

    @Setter(AccessLevel.PROTECTED)
    @Column(name = "process_info_3", columnDefinition = "longtext COMMENT '处理过程信息3'")
    private String processInfo3;

    @Type(MapContentUT.class)
    @Column(name = "properties", columnDefinition = "varchar(511) COMMENT '流程属性配置'")
    private MapContent properties = new MapContent();

    private final static ThreadLocalStack<Long> runningIdStack = new ThreadLocalStack<>();

    protected Generate(GenerateConfig config, JSONObject request, Flow flow) {
        this.request = request;
        this.config = config;
        this.flow = flow;
        this.parentGenerate = getRunningGenerate();
        this.properties = config.getProperties();
        FunctionCall functionCall = getRunningFunctionCall();
        if (functionCall != null) functionCall.setChildGenerate(this);
    }

    public static Generate newGenerate(GenerateConfig config, JSONObject request) {
        return newGenerate(config, request, null);
    }

    public static Generate newGenerate(GenerateConfig config, Flow flow) {
        return newGenerate(config, flow.getRequest(), flow);
    }

    private static Generate newGenerate(GenerateConfig config, JSONObject request, Flow flow) {
        return switch (config.getGenerateType()) {
            case LLM_GEN -> new Generate4LlmGen(config, request, flow);
            case SALE_COMPARE -> new Generate4SaleCompare(config, request, flow);
            case POLICY_COMMENT -> new Generate4PolicyComment(config, request, flow);
            case LIFE_EXPERT -> new Generate4LifeExpert(config, request, flow);
            case HOUSING_EXPERT -> new Generate4HousingExpert(config, request, flow);
            case MONTHLY_REPORT -> new Generate4MonthlyMarket(config, request, flow);
            case INDUSTRY_ANALYST -> new Generate4IndustryPolicies(config, request, flow);
            default -> throw new RuntimeException("测试生成任务不支持");
        };
    }

    /**
     * 执行生成任务
     */
    public void exec() {
        if (isCompleted()) {
            log.warn("生成任务已完成，无需重复执行: {}", getId());
            return;
        }

        try {
            log.info("执行生成任务: {}", id);
            timerStart();
            runningIdStack.push(id);
            this.status = doExec();
        } catch (Exception e) {
            log.error("生成任务执行失败: {}", id, e);
            this.setErrorMessage(e.getMessage());
            this.status = FAILED;
            if (flow != null) flow.makeCompleted(this);
        } finally {
            if (isCompleted()) timerStop();
            Long popId = runningIdStack.pop();
            if (!id.equals(popId))
                log.error("堆栈弹出异常: 预期id({}),实际id({})", id, popId);
        }
    }


    /**
     * 是否完成
     */
    public boolean isCompleted() {
        return status == SUCCESS ||
               status == GenerateDTO.Status.FAILED ||
               status == GenerateDTO.Status.CANCELLED;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status == SUCCESS;
    }

    public GenerateDTO.Type getType() {
        if (type == null)
            type = GenerateDTO.Type.valueOf(ClassUtils.getClassAnnotation(this.getClass(), DiscriminatorValue.class).value());
        return type;
    }

    public static Generate getRunningGenerate() {
        Long generateId = runningIdStack.peek();
        return generateId != null ? getBean(GenerateRpt.class).getReferenceById(generateId) : null;
    }

    public String getPrevGenerateResponse() {
        if (flow == null) throw new CodingException("生成过程[%s]不关联任何工作流,无法获取前序生成应答!", getId());
        return flow.getPrevGenerateResponse(this);
    }

    public void receiveAsyncResponse(GenerateDTO.Status status, String response) {
        if (this.status != GENERATING) {
            log.warn("重复收到预处理结果: {}", response);
            return;
        }
        log.info("收到预处理结果: {}", response);
        this.setResponse(response);
        this.status = status;
        timerStop();
    }

    public Long getFlowId() {
        return flow != null ? flow.getId() : null;
    }

    protected abstract GenerateDTO.Status doExec() throws Exception;

    public int getResponseLength() {
        return getResponse() != null ? getResponse().length() : 0;
    }

    public boolean isHasFlow() {
        return flow != null;
    }

    public boolean isHasParentFlow() {
        return flow != null && flow.hasParentFlow();
    }

    public Flow getParentFlow() {
        return flow.getParentFlow();
    }

    @Override
    public ProcessDTO.Type getProcessType() {
        return ProcessDTO.Type.GENERATE;
    }

}
